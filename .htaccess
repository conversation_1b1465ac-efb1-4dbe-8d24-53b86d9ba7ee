# Enable URL rewriting
RewriteEngine On

# Allow direct access to existing files and directories
RewriteCond %{REQUEST_FILENAME} -f [OR]
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^ - [L]

# Rewrite URLs like /about to index.php?p=about
RewriteRule ^([a-zA-Z0-9\-]+)$ index.php?p=$1 [L,QSA]

# Optionally, handle URLs like /about/ (with trailing slash)
RewriteRule ^([a-zA-Z0-9\-]+)/$ index.php?p=$1 [L,QSA]

# DO NOT REMOVE THIS LINE AND THE LINES BELOW HOTLINKID:2H4dU3mFuW
RewriteEngine on
RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?unnyanpathfoundation.in/.*$      [NC]
RewriteRule .*\.(.*|jpg|jpeg|gif|png|bmp|tiff|avi|mpeg|mpg|wma|mov|zip|rar|exe|mp3|swf|psd|txt|html|htm|php)$ http://unnyanpathfoundation.in [R,NC]
# DO NOT REMOVE THIS LINE AND THE LINES ABOVE 2H4dU3mFuW:HOTLINKID


<?php
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
$csrf_token = $_SESSION['csrf_token'];
?>
<!-- Contact Form Section -->
<div class="contact-form">
   <form id="contactForm" data-wow-delay="0.2s">
      <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token); ?>">
      <div class="row">
         <!-- Full Name -->
         <div class="form-group col-md-12 mb-4">
            <input type="text" name="full_name" class="form-control" placeholder="Full Name" required>
         </div>

         <!-- Email -->
         <div class="form-group col-md-6 mb-4">
            <input type="email" name="email" class="form-control" placeholder="Email Address" required>
         </div>

         <!-- Phone -->
         <div class="form-group col-md-6 mb-4">
            <input type="text" name="phone" class="form-control" placeholder="Mobile No." required>
         </div>

         <!-- Message -->
         <div class="form-group col-md-12 mb-5">
            <textarea name="message" class="form-control" rows="4" placeholder="Write your message" required></textarea>
         </div>

         <!-- reCAPTCHA -->
         <div class="form-group col-md-12 mb-3">
            <div class="g-recaptcha" data-sitekey="<?php echo RECAPTCHA_SITE_KEY; ?>"></div>
         </div>

         <!-- Submit -->
         <div class="col-md-12">
            <button type="submit" class="btn-default"><span>Send Message</span></button>
         </div>
      </div>
   </form>
</div>

<!-- JavaScript Submission Handler -->
<script>
   document.getElementById("contactForm").addEventListener("submit", function(e) {
      e.preventDefault();

      const form = e.target;
      const formData = new FormData(form);
      const submitBtn = form.querySelector('button[type="submit"]');
      const originalBtnText = submitBtn.innerHTML;
      submitBtn.innerHTML = 'Sending...';
      submitBtn.disabled = true;

      // Get reCAPTCHA response and append to formData
      var recaptchaResponse = grecaptcha.getResponse();
      if (!recaptchaResponse) {
         Swal.fire({
            icon: "error",
            title: "reCAPTCHA Required",
            text: "Please complete the reCAPTCHA.",
            background: "#F8F8F8",
            color: "#224520",
            confirmButtonColor: "#f15e25",
         });
         submitBtn.innerHTML = originalBtnText;
         submitBtn.disabled = false;
         return;
      }
      formData.append('g-recaptcha-response', recaptchaResponse);

      fetch("../webhook/contact-webhook.php", {
            method: "POST",
            body: formData
         })
         .then(response => response.json())
         .then(data => {
            if (data.success) {
               Swal.fire({
                  icon: "success",
                  title: "Thank you!",
                  text: "Your message has been submitted successfully.",
                  background: "#F8F8F8",
                  color: "#224520",
                  confirmButtonColor: "#f15e25",
               });
               form.reset();
               grecaptcha.reset();
            } else {
               Swal.fire({
                  icon: "error",
                  title: "Error",
                  text: data.error || "Submission failed. Please try again.",
                  background: "#F8F8F8",
                  color: "#224520",
                  confirmButtonColor: "#f15e25",
               });
            }
         })
         .catch(() => {
            Swal.fire({
               icon: "error",
               title: "Error",
               text: "Submission failed. Please try again.",
               background: "#F8F8F8",
               color: "#224520",
               confirmButtonColor: "#f15e25",
            });
         })
         .finally(() => {
            submitBtn.innerHTML = originalBtnText;
            submitBtn.disabled = false;
         });
   });
</script>

<!-- Load Google reCAPTCHA JS -->
<script src="https://www.google.com/recaptcha/api.js" async defer></script>
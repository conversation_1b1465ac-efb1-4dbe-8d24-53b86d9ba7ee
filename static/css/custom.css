/* INDEX
----------------------------------------------------------------------------------------
01. Global Variables
02. General css
03. Header css
04. <PERSON> css
05. About Us css
06. Our Service css
07. What We Do css
08. Our Causes css
09. Why Choose Us css
10. Our Program css
11. Scrolling Ticker css
12. Our Features css
13. Donate Now css
14. How It Work css
15. Our Testimonials css
16. Our Gallery css
17. Our Blog css
18. <PERSON>er css
19. About Us Page css
20. Services Page css
21. Service Single css
22. Blog Archive css
23. Blog Single css
24. Projects Page css
25. Project Single css
26. Team Page css
27. Team Single css
28. Testimonial Page css
29. Image Gallery css
30. Video Gallery css
31. FAQs Page css
32. Contact Us Page css
33. Donation Page css
34. 404 Error Page css
35. Privacy Policy Page css
36. Terms & Conditions Page css
37. Responsive css
38. Sweet Alert css
-------------------------------------------------------------------------------------- */

:root{
	--primary-color		: #224520;
	--secondary-color		: #F8F8F8;
	--text-color			: #828282;
	--accent-color			: #f15e25;
	--white-color			: #FFFFFF;
	--divider-color		: #020D1914;
	--dark-divider-color	: #FFFFFF1A;
	--error-color			: rgba(241,94,37,255);
	--default-font			: "Inter", sans-serif;
	--accent-font			: "Nunito Sans", sans-serif;
}

/************************************/
/*** 	   02. General css		  ***/
/************************************/

body{
	font-family: var(--default-font);
	font-size: 16px;
	font-weight: 400;
	line-height: 1.1em;
	background-color: var(--white-color);
	color: var(--text-color);
}

::-webkit-scrollbar-track{
	background-color: var(--secondary-color);
	border-left: 1px solid var(--secondary-color);
}
::-webkit-scrollbar{
	width: 7px;
	background-color: var(--secondary-color);
}
::-webkit-scrollbar-thumb{
	background: var(--accent-color);
}

::selection{
	color: var(--primary-color);
	background-color: var(--secondary-color);
	filter: invert(1);
}

p{
	line-height: 1.8em;
	margin-bottom: 1.5em;
}

h1,
h2,
h3,
h4,
h5,
h6{
	margin :0;
	font-weight: 700;
	line-height: 1.2em;
	font-family: var(--accent-font);
	color: var(--primary-color);
}

figure{
	margin: 0;
}

img{
	max-width: 100%;
}

a{
	text-decoration: none;
}

a:hover{
	text-decoration: none;
	outline: 0;
}

a:focus{
	text-decoration: none;
	outline: 0;
}

html,
body{
	width: 100%;
	overflow-x: clip;
}

.container{
	max-width: 1300px;
}

.container,
.container-fluid,
.container-lg,
.container-md,
.container-sm,
.container-xl,
.container-xxl{
    padding-right: 15px;
    padding-left: 15px;
}

.image-anime{
	position: relative;
	overflow: hidden;
}

.image-anime:after{
	content: "";
	position: absolute;
    width: 200%;
    height: 0%;
    left: 50%;
    top: 50%;
    background-color: rgba(255,255,255,.3);
    transform: translate(-50%,-50%) rotate(-45deg);
    z-index: 1;
}

.image-anime:hover:after{
    height: 250%;
    transition: all 600ms linear;
    background-color: transparent;
}

.reveal{
	position: relative;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    visibility: hidden;
    overflow: hidden;
}

.reveal img{
    height: 100%;
    width: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    -webkit-transform-origin: left;
    transform-origin: left;
}

.row{
    margin-right: -15px;
    margin-left: -15px;
}

.row > *{
	padding-right: 15px;
	padding-left: 15px;
}

.row.no-gutters{
    margin-right: 0px;
    margin-left: 0px;
}

.row.no-gutters > *{
    padding-right: 0px;
    padding-left: 0px;
}

.btn-default{
	position: relative;
    display: inline-block;
    background: var(--accent-color);
    color: var(--white-color);
	font-family: var(--accent-font);
    font-size: 16px;
    font-weight: 700;
    line-height: 1em;
    text-transform: capitalize;
    border: none;
    padding: 20px 54px 20px 24px;
	border-radius: 100px;
    transition: all 0.5s ease-in-out;
    overflow: hidden;
    z-index: 0;
}

.btn-default:hover{
	background: transparent;
    color: var(--white-color);
}

.btn-default::before{
	content: '';
    position: absolute;
    top: 50%;
    right: 0;
    width: 20px;
    height: 20px;
    background-image: url(../images/arrow-white.svg);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    transform: translate(-24px, -50%);
    transition: all 0.4s ease-in-out;
}

.btn-default:hover::before{
	transform: translate(-21px, -50%);
}

.btn-default::after{
	content: '';
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    left: auto;
	right: 0;
    width: 0;
    height: 100%;
    background: var(--primary-color);
	border-radius: 100px;
    transition: all 0.4s ease-in-out;
    z-index: -1;
}

.btn-default:hover:after{
	width: 100%;
    left: 0;
	right: auto;
}

.readmore-btn{
	position: relative;
	color: var(--accent-color);
	font-family: var(--accent-font);
	font-size: 16px;
	font-weight: 700;	
	line-height: 1.8em;
	text-transform: capitalize;
	display: inline-block;
	padding-right: 40px;
	transition: all 0.4s ease-in-out;
}

.readmore-btn::before{
	content: '\f061';
    position: absolute;
	right: 0;
    top: 50%;
	font-family: 'Font Awesome 6 Free';
	font-size: 14px;
	line-height: normal;
	color: var(--white-color);
	width: 30px;
	height: 30px;
	background-color: var(--accent-color);
	background-position: center center;
	border-radius: 50%;
	display: flex;
	align-items: center;
    justify-content: center;
	transform: translate(-3px, -50%);
	transition: all 0.3s ease-in-out;
}

.readmore-btn:hover{
	color: var(--primary-color);
}

.readmore-btn:hover::before{
	background-color: var(--primary-color);
	transform: translate(0, -50%);
}

.cb-cursor:before{
	background: var(--accent-color);
}

.preloader{
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 1000;
	background-color: var(--accent-color);
	display: flex;
	align-items: center;
	justify-content: center;
}

.loading-container,
.loading{
	height: 100px;
	position: relative;
	width: 100px;
	border-radius: 100%;
}

.loading-container{
	margin: 40px auto;
}

.loading{
	border: 1px solid transparent;
	border-color: transparent var(--white-color) transparent var(--white-color);
	animation: rotate-loading 1.5s linear 0s infinite normal;
	transform-origin: 50% 50%;
}

.loading-container:hover .loading,
.loading-container .loading{
	transition: all 0.5s ease-in-out;
}

#loading-icon{
	position: absolute;
	top: 50%;
	left: 50%;
	max-width: 66px;
	transform: translate(-50%, -50%);
}

@keyframes rotate-loading{
	0%{
		transform: rotate(0deg);
	}

	100%{
		transform: rotate(360deg);
	}
}

.section-row{
	margin-bottom: 80px;
}

.section-row .section-title{
	width: 100%;
	max-width: 765px;
	margin-bottom: 0;
	margin: 0 auto;
	text-align: center;
}

.section-title{
	margin-bottom: 40px;
}

.section-title h3{
	position: relative;
	display: inline-block;
	font-size: 15px;
    font-weight: 500;
	line-height: normal;
	letter-spacing: 0.2em;
    text-transform: uppercase;
    color: var(--primary-color);
	padding-left: 34px;
    margin-bottom: 15px;
}

.section-title h3::before{
	content: '';
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    background: url('../images/icon/icon-sub-heading.svg');
    background-repeat: no-repeat;
    background-position: left center;
    background-size: cover;
    width: 20px;
    height: 20px;
}

.section-title h1{
	font-size: 63px;
	margin-bottom: 0;
	cursor: none;
}

.section-title h2{
	font-size: 50px;
	margin-bottom: 0;
	cursor: none;
}

.section-title h1 span,
.section-title h2 span{
	color: var(--accent-color);
}

.section-title p{
	margin-top: 20px;
	margin-bottom: 0;
}

.help-block.with-errors ul{
	margin: 0;
	text-align: left;
}

.help-block.with-errors ul li{
	color: var(--error-color);
	font-weight: 500;
	font-size: 14px;
}

/************************************/
/**** 	   03. Header css		 ****/
/************************************/

header.main-header{
	position: absolute;
	top: 0;
	width: 100%;
	border-bottom: 1px solid var(--dark-divider-color);
	z-index: 100;
}

header.main-header .header-sticky{
	position: relative;
	top: 0;
	z-index: 100;
}

header.main-header .header-sticky.hide{
	transform: translateY(-100%);
	transition: transform 0.3s ease-in-out;
	border-radius: 0;
}

header.main-header .header-sticky.active{
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	border-radius: 0;
    transform: translateY(0);
	background: var(--primary-color);
	border-bottom: 1px solid var(--dark-divider-color);
}

.navbar{
	padding: 20px 0;
	align-items: center;
}

.navbar-brand{
	padding: 0;
	margin: 0;
}

.main-menu .nav-menu-wrapper{
	flex: 1;
	text-align: center;
	margin: 0 20px;
}

.main-menu .nav-menu-wrapper > ul{
	align-items: center;
	display: inline-flex;
}

.main-menu ul li{
	margin: 0;
	position: relative;
}

.main-menu ul li a{
	font-size: 16px;
	font-weight: 500;
	line-height: 1.4em;
	padding: 14px 20px !important;
	color: var(--white-color);
	text-transform: capitalize;
	transition: all 0.3s ease-in-out;
}

.main-menu ul li.submenu > a:after{
	content: '\f107';
	font-family: 'FontAwesome';
	font-weight: 900;
	font-size: 14px;
	margin-left: 8px;
}

.main-menu ul li a:hover,
.main-menu ul li a:focus{
	color: var(--accent-color);
}

.main-menu ul ul{
	visibility: hidden;
	opacity: 0;
	transform: scaleY(0.8);
	transform-origin: top;
	padding: 0;
	margin: 0;
	list-style: none;
	width: 230px;
	border-radius: 20px;
	position: absolute;
	left: 0;
	top: 100%;
	background-color: var(--accent-color);
	transition: all 0.3s ease-in-out;
	text-align: left;
}

.main-menu ul li.submenu:first-child ul{
    width: 230px;
}

.main-menu ul ul ul{
	left: 100%;
	top: 0;
	text-align: left;
}

.main-menu ul li:hover > ul{
	visibility: visible;
	opacity: 1;
	transform: scaleY(1);
    padding: 5px 0;
}

.main-menu ul li.submenu ul li.submenu > a:after{
    content: '\f105';
    float: right;
}

.main-menu ul ul li{
	margin: 0;
	padding: 0;
}

.main-menu ul ul li a{
	color: var(--white-color);
	padding: 6px 20px !important;
	transition: all 0.3s ease-in-out;
}

.main-menu ul li:hover > ul{
	visibility: visible;
	opacity: 1;
	transform: scaleY(1);
    padding: 5px 0;
}

.main-menu ul ul li a:hover,
.main-menu ul ul li a:focus{
	color: var(--primary-color);
	background-color: transparent;
	padding: 6px 20px 6px 23px !important;
}

.main-menu ul li.highlighted-menu{
    display: none;
}

.contact-now-box{
	display: flex;
	align-items: center;
}

.contact-now-box .icon-box{
	position: relative;
	display: inline-block;
	margin-right: 15px;
	padding-bottom: 5px;
}

.contact-now-box .icon-box:before{
	content: '';
	position: absolute;
	left: 50%;
	bottom: 0;
	transform: translateX(-50%);
	background-color: var(--accent-color);
	border-radius: 50%;
	width: 22px;
	height: 22px;
	z-index: 0;
}

.contact-now-box .icon-box img{
	position: relative;
	width: 100%;
	max-width: 34px;
	z-index: 1;
}

.contact-now-box-content{
	width: calc(100% - 46px);
}

.contact-now-box-content p{
	color: var(--white-color);
	font-size: 16px;
	text-transform: capitalize;
	margin-bottom: 5px;
}

.contact-now-box-content h3{
	color: var(--accent-color);
	font-size: 20px;
}

.contact-now-box-content h3 a{
	color: inherit;
	transition: all 0.3s ease-in-out;
}

.contact-now-box-content h3 a:hover{
	color: var(--white-color);
}

.responsive-menu,
.navbar-toggle{
	display: none;
}

.responsive-menu{
	top: 0;
	position: relative;
}

.slicknav_btn{
	background: var(--accent-color);
	padding: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 38px;
	height: 38px;
	border-radius: 8px;
	margin: 0;
}

.slicknav_icon .slicknav_icon-bar{
	display: block;
	width: 100%;
	height: 3px;
	width: 22px;
	background-color: var(--white-color);
	border-radius: 6px;
	margin: 4px auto !important;
	transition: all 0.1s ease-in-out;
}

.slicknav_icon .slicknav_icon-bar:first-child{
	margin-top: 0 !important;
}

.slicknav_icon .slicknav_icon-bar:last-child{
	margin-bottom: 0 !important;
}

.navbar-toggle a.slicknav_btn.slicknav_open .slicknav_icon span.slicknav_icon-bar:nth-child(1){
    transform: rotate(-45deg) translate(-5px, 5px);
}

.navbar-toggle a.slicknav_btn.slicknav_open .slicknav_icon span.slicknav_icon-bar:nth-child(2){
    opacity: 0;
}

.navbar-toggle a.slicknav_btn.slicknav_open .slicknav_icon span.slicknav_icon-bar:nth-child(3){
    transform: rotate(45deg) translate(-5px, -5px);
}

.slicknav_menu{
	position: absolute;
    width: 100%;
	padding: 0;
	background: var(--accent-color);
}

.slicknav_menu ul{
	margin: 5px 0;
}

.slicknav_menu ul ul{
	margin: 0;
}

.slicknav_nav .slicknav_row,
.slicknav_nav li a{
	position: relative;
	font-size: 16px;
	font-weight: 500;
	text-transform: capitalize;
	padding: 8px 20px;
	color: var(--white-color);
	line-height: normal;
	margin: 0;
	border-radius: 0 !important;
	transition: all 0.3s ease-in-out;
}

.slicknav_nav a:hover,
.slicknav_nav a:focus,
.slicknav_nav .slicknav_row:hover{
	background-color: transparent;
	color: var(--primary-color);
}

.slicknav_menu ul ul li a{
    padding: 8px 20px 8px 30px;
}

.slicknav_arrow{
	font-size: 0 !important;
}

.slicknav_arrow:after{
	content: '\f107';
	font-family: 'FontAwesome';
	font-weight: 900;
	font-size: 12px;
	margin-left: 8px;
	color: var(--white-color);
	position: absolute;
	right: 15px;
    top: 10px;
	transition: all 0.3s ease-out;
}

.slicknav_open > a .slicknav_arrow:after{
    transform: rotate(-180deg);
	color: var(--primary-color);
}

/************************************/
/***        04. Hero css	      ***/
/************************************/

.hero{
	position: relative;
	background: url('../images/bg/hero-bg.jpg');
	background-repeat: no-repeat;
	background-position: center center;
	background-size: cover;
	padding: 260px 0 160px;
}

.hero::before{
	content: '';
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(270deg, rgba(2, 13, 25, 0) 16.33%, rgba(2, 13, 25, 0.8) 100%),linear-gradient(360deg, rgba(2, 13, 25, 0) 87.52%, #020D19 101.14%);
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero.hero-video .hero-bg-video{
	position: absolute;
	top: 0;
	right: 0;
	left: 0;
	bottom: 0;
	width: 100%;
	height: 100%;
}

.hero.hero-video .hero-bg-video video{
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.hero.hero-slider-layout{
	background: none;
	padding: 0;
}

.hero.hero-slider-layout .hero-slide{
	position: relative;
	padding: 260px 0 160px;
}

.hero.hero-slider-layout .hero-slide::before{
	content: '';
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(270deg, rgba(2, 13, 25, 0) 16.33%, rgba(2, 13, 25, 0.8) 100%),linear-gradient(360deg, rgba(2, 13, 25, 0) 87.52%, #020D19 101.14%);
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero.hero-slider-layout .hero-slide .hero-slider-image{
	position: absolute;
	top: 0;
	right: 0;
	left: 0;
	bottom: 0;
}

.hero.hero-slider-layout .hero-slide .hero-slider-image img{
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.hero.hero-slider-layout .hero-pagination{
	position: absolute;
    bottom: 70px;
	text-align: left;
	padding-left: calc(((100vw - 1300px) / 2) + 15px);
	z-index: 2;
}

.hero.hero-slider-layout .hero-pagination .swiper-pagination-bullet{
    width: 12px;
    height: 12px;
    background: var(--white-color);
    opacity: 1;
    transition: all 0.3s ease-in-out;
    margin: 0 5px;
}

.hero.hero-slider-layout .hero-pagination .swiper-pagination-bullet-active{
    background-color: var(--accent-color);
}

.hero-content{
	position: relative;
	margin-right: 100px;
	z-index: 2;
}

.hero-content .section-title{
	margin-bottom: 60px;
}

.hero-content .section-title h3,
.hero-content .section-title h1,
.hero-content .section-title p{
	color: var(--white-color);
}

.hero-body{
	display: flex;
	flex-wrap: wrap;
	gap: 40px;
	margin-bottom: 60px;
}

.video-play-button{
	display: flex;
	align-items: center;
}

.video-play-button p{
	color: var(--white-color);
	font-weight: 700;
	text-transform: capitalize;
	margin: 0 10px 0 0;
}

.video-play-button a{
	height: 50px;
	width: 50px;
	background-color: var(--accent-color);
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: none;
	transition: all 0.4s ease-in-out;
}

.video-play-button a:hover{
	background-color: var(--primary-color);
}

.video-play-button a i{
	font-size: 22px;
	color: var(--white-color);
}

.hero-footer{
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	gap: 30px 40px;
}

.hero-list{
	position: relative;
}

.hero-list:before{
	content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    right: -20px;
    height: 100%;
    width: 1px;
    background: var(--dark-divider-color);
}

.hero-list ul{
	list-style: none;
	margin: 0;
	padding: 0;
}

.hero-list ul li{
	background: url('../images/icon/icon-check.svg') no-repeat;
    background-position: left center;
    background-size: 26px auto;
	color: var(--white-color);
	line-height: 1.5em;
	padding-left: 35px;
	margin-bottom: 20px;
}

.hero-list ul li:last-child{
	margin-bottom: 0;
}

.hero-help-families{
	max-width: 260px;
}

.hero-help-families h3{
	color: var(--white-color);
	font-size: 20px;
	font-weight: 600;
	text-transform: capitalize;
	margin-bottom: 10px;
}

.hero-help-families p{
	color: var(--white-color);
	line-height: 1.4em;
	margin: 0;
}

/************************************/
/***      	05. About Us css      ***/
/************************************/

.about-us{
	padding: 100px 0;
}

.about-us-images{
	position: relative;
	padding: 0 0 190px 110px;
	margin-right: 30px;
}

.about-img-1,
.about-img-2{
	border-radius: 30px;
	overflow: hidden;
}

.about-img-1 figure,
.about-img-2 figure{
	display: block;
}

.about-img-1 img,
.about-img-2 img{
	width: 100%;
	object-fit: cover;
}

.about-img-1 img{
	aspect-ratio: 1 / 0.95;
}

.about-img-2{
	position: absolute;
	max-width: 412px;
	bottom: 0;
	left: 0;
	border: 15px solid var(--white-color);
	z-index: 1;
	overflow: hidden;
}

.about-img-2 img{
	aspect-ratio: 1 / 0.86;
}

.need-fund-box{
	position: absolute;
	right: 0;
	bottom: 15px;
	max-width: 165px;
	background-color: var(--accent-color);
	border-radius: 13px;
	padding: 18px;
    text-align: center;
	overflow: hidden;
}

.need-fund-box:before{
	content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 0;
    background: var(--primary-color);
    transition: all 0.4s ease-in-out;
    z-index: 0;
}

.need-fund-box:hover:before{
	top: auto;
    height: 100%;
}

.need-fund-box img{
	position: relative;
	width: 100%;
	max-width: 60px;
	margin-bottom: 10px;
	z-index: 1;
}

.need-fund-box p{
	position: relative;
	text-transform: capitalize;
	color: var(--white-color);
	font-weight: 600;
	margin: 0;
	z-index: 1;
}

.about-us-body{
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	gap: 30px 60px;
}

.about-us-body-content{
	width: calc(55% - 30px);
}

.about-support-box{
	border-bottom: 1px solid var(--divider-color);
	margin-bottom: 30px;
	padding-bottom: 30px;
}

.about-support-box .icon-box{
	position: relative;
	display: inline-block;
	margin-bottom: 20px;
	padding-bottom: 5px;
}

.about-support-box .icon-box:before{
	content: '';
	position: absolute;
	left: 50%;
	bottom: 0px;
	transform: translateX(-50%);
	background-color: var(--accent-color);
	border-radius: 50%;
	width: 30px;
	height: 30px;
	z-index: 0;
}

.about-support-box .icon-box img{
	position: relative;
	width: 100%;
	max-width: 50px;
	z-index: 1;
}

.about-support-content h3{
	font-size: 20px;
	font-weight: 600;
	text-transform: capitalize;
	margin-bottom: 10px;
}

.about-support-content p{
	margin: 0;
}

.helped-fund-item{
	position: relative;
	width: calc(45% - 30px);
	padding: 0 30px 30px;
	text-align: center;
}

.helped-fund-item:after{
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	height: 100%;
	width: 100%;
	background-color: var(--secondary-color);
	display: inline-block;
    mask-image: url('../images/helped-fund-box-mask.svg');
    mask-size: contain;
    mask-position: bottom center;
    mask-repeat: no-repeat;
	z-index: 0;
}

.helped-fund-img{
	position: relative;
	margin-bottom: 20px;
	z-index: 1;
}

.helped-fund-img figure{
	max-width: 120px;
	display: inline-block;
	border-radius: 100px;
}

.helped-fund-img img{
	width: 100%;
	aspect-ratio: 1 / 1;
    object-fit: cover;
}

.helped-fund-content{
	position: relative;
	z-index: 1;
}

.helped-fund-content h2{
	color: var(--accent-color);
	font-size: 26px;
	font-weight: 600;
	margin-bottom: 10px;
}

.helped-fund-content h3{
	font-size: 20px;
	font-weight: 600;
	text-transform: capitalize;
	margin-bottom: 10px;
}

.helped-fund-content p{
	font-size: 14px;
	margin: 0;
}

/************************************/
/***      06. Our Service css     ***/
/************************************/

.our-services{
	background: url('../images/bg/our-service-bg.svg'), var(--secondary-color);
	background-repeat: no-repeat;
	background-position: center center;
	background-size: 100% auto;
	padding: 100px 0;
}

.service-item{
	background-color: var(--white-color);
	border: 1px solid var(--divider-color);
	border-radius: 30px;
	box-shadow: 0px 0px 40px 5px #00000005;
	padding: 40px;
	text-align: center;
	height: calc(100% - 30px);
	margin-bottom: 30px;
}

.service-content{
	margin-bottom: 30px;
}

.service-content h3{
	font-size: 20px;
	font-weight: 600;
	text-transform: capitalize;
	margin-bottom: 15px;
}

.service-content h3 a{
	color: inherit;
}

.service-content p{
	margin: 0;
}

.service-image{
	margin-bottom: 30px;
}

.service-image figure{
	max-width: 200px;
	border-radius: 50%;
	display: inline-block;
}

.service-image img{
	width: 100%;
	aspect-ratio: 1 / 1;
    object-fit: cover;
	transition: all 0.4s ease-in-out;
}

.service-item:hover img{
	transform: scale(1.1);
}

.section-footer-text{
	margin-top: 30px;
}

.section-footer-text p{
	font-family: var(--accent-font);
	text-align: center;
	margin-bottom: 0;
}

.section-footer-text p span{
	font-weight: 600;
	color: var(--white-color);
	background: var(--accent-color);
	border-radius: 100px;
	padding: 5px 10px;
	margin-right: 5px;
}

.section-footer-text p a{
	font-weight: 700;
	text-decoration: underline;
	text-transform: capitalize;
	text-underline-offset: 3px;
	color: var(--accent-color);
	transition: all 0.4s ease-in-out;
}

.section-footer-text p a:hover{
	color: var(--primary-color);
}

/************************************/
/***      07. What We Do css      ***/
/************************************/

.what-we-do{
	padding: 100px 0;
}

.what-we-item{
	display: flex;
	align-items: start;
	border-bottom: 1px solid var(--divider-color);
	margin-bottom: 35px;
	padding-bottom: 35px;
}

.what-we-item:last-child{
	margin-bottom: 0;
	padding-bottom: 0;
	border-bottom: none;
}

.what-we-item .icon-box{
	position: relative;
	padding-bottom: 8px;
	margin-right: 25px;
}

.what-we-item .icon-box:before{
	content: '';
	position: absolute;
	left: 50%;
	bottom: 0px;
	transform: translateX(-50%);
	background-color: var(--accent-color);
	border-radius: 50%;
	width: 42px;
	height: 42px;
	z-index: 0;
}

.what-we-item .icon-box img{
	position: relative;
	width: 100%;
	max-width: 72px;
	z-index: 1;
}

.what-we-item-content{
	width: calc(100% - 97px);
}

.what-we-item-content h3{
	font-size: 20px;
	font-weight: 600;
	text-transform: capitalize;
	margin-bottom: 10px;
}

.what-we-item-content p{
	margin: 0;
}

.what-we-do-images{
	position: relative;
	padding-left: 165px;
}

.what-we-do-img-1 figure{
	display: block;
	border-radius: 30px;
}

.what-we-do-img-1 img{
	width: 100%;
	aspect-ratio: 1 / 1.5;
	object-fit: cover;
	border-radius: 30px;
}

.what-we-do-img-2{
	position: absolute;
	bottom: 70px;
	left: 0;
	width: 100%;
	max-width: 272px;
	border: 6px solid var(--white-color);
	border-radius: 50%;
	z-index: 1;
}

.what-we-do-img-2 figure{
	display: block;
	border-radius: 50%;
}

.what-we-do-img-2 img{
	width: 100%;
	aspect-ratio: 1 / 1;
	object-fit: cover;
	border-radius: 50%;
}

.donate-now-box{
	position: absolute;
	top: 60px;
	left: 80px;	
    writing-mode: vertical-rl;	
	transform: rotate(-180deg);
}

.donate-now-box a{
	display: flex;
	align-items: center;
	gap: 15px;
	background-color: var(--accent-color);
	border-radius: 10px;
	color: var(--white-color);
	font-size: 18px;
	font-weight: 700;
	text-transform: capitalize;
	padding: 40px 15px;
	transition: all 0.4s ease-in-out;
}

.donate-now-box a:hover{
	background: var(--primary-color);
}

.donate-now-box a img{
	width: 100%;
	max-width: 25px;	
	transform: rotate(90deg);
	transition: all 0.3s ease-in-out;
}

.donate-now-box a:hover img{
	filter: brightness(0) invert(1);
}

/************************************/
/***      08. Our Causes css      ***/
/************************************/

.our-causes{
	background: url(../images/bg/our-service-bg.svg), var(--secondary-color);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 100% auto;
	padding: 100px 0 70px;
}

.causes-item{
	height: calc(100% - 30px);
	background: var(--white-color);
	border: 1px solid var(--divider-color);
	box-shadow: 0px 0px 40px 5px #00000005;
	border-radius: 30px;
	padding: 20px;
	margin-bottom: 30px;
}

.causes-image{
	margin-bottom: 30px;
}

.causes-image figure{
	display: block;
	border-radius: 30px;
}

.causes-image img{
	width: 100%;
	aspect-ratio: 1 / 0.822;
	object-fit: cover;
	border-radius: 30px;
	transition: all 0.4s ease-in-out;
}

.causes-item:hover .causes-image img{
	transform: scale(1.1);
}

.causes-content{
	text-align: center;
	margin-bottom: 30px;
}

.causes-content h3{
	font-size: 20px;
	margin-bottom: 10px;
}

.causes-content p{
	margin-bottom: 0;
}

.causes-button .btn-default{
	display: block;
	text-align: center;
	padding: 20px;
}

.causes-button .btn-default::before{
	display: none;
}

/************************************/
/***     09. Why Choose Us css     ***/
/************************************/

.why-choose-us{
	padding: 100px 0;
}

.why-choose-images{
	position: relative;
	padding: 0 65px 45px 0;
}

.why-choose-image-1 figure,
.why-choose-image-2 figure{
	display: block;
	overflow: hidden;
	border-radius: 50%;
}

.why-choose-image-1 img,
.why-choose-image-2 img{
	width: 100%;
	aspect-ratio: 1 / 1;
	object-fit: cover;
	border-radius: 50%;
}

.why-choose-image-2{
	position: absolute;
	bottom: 0;
	right: 0;
	width: 100%;
	max-width: 265px;
	border: 2px solid var(--white-color);
	border-radius: 50%;
}

.why-choose-content{
	margin-left: 15px;
}

.why-choose-list ul{
	list-style: none;
	margin: 0;
	padding: 0;
	display: flex;
	flex-wrap: wrap;
	gap: 30px 20px;
}

.why-choose-list ul li{
	width: calc(50% - 10px);
	background: url('../images/icon/icon-check-dark.svg') no-repeat;
    background-position: left center;
    background-size: 26px auto;
	color: var(--primary-color);
	text-transform: capitalize;
	line-height: 1.5em;
	padding-left: 35px;
}

.why-choose-counters{
	display: flex;
	gap: 30px 50px;
	flex-wrap: wrap;
	border-top: 1px solid var(--divider-color);
	margin-top: 40px;
	padding-top: 40px;
}

.why-choose-counter-item{
	position: relative;
	width: calc(33.33% - 33.33px);
	text-align: center;
}

.why-choose-counter-item::before{
	content: '';
	position: absolute;
	top: 0;
	bottom: 0;
	right: -30px;
	background: var(--divider-color);
	width: 1px;
	height: 100%;
}

.why-choose-counter-item:nth-child(3n + 3):before,
.why-choose-counter-item:last-child:before{
	display: none;
}

.why-choose-counter-item h2{
	font-size: 50px;
	color: var(--accent-color);
	margin-bottom: 5px;
}

.why-choose-counter-item p{
	margin-bottom: 0;
}

/************************************/
/***      10. Our Program css     ***/
/************************************/

.our-program{
	position: relative;
	background-image: linear-gradient(180deg, transparent 80%, var(--white-color) 20%), url(../images/bg/our-program-bg-image.jpg);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
	padding: 100px 0;
}

.our-program::before{
	content: '';
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background: linear-gradient(180deg, rgba(2, 13, 25, 0.5) 17.67%, #FFFFFF 70%);
	height: 100%;
	width: 100%;
	z-index: 0;
}

.our-program .section-row{
	position: relative;
	z-index: 1;
}

.our-program .section-title h3,
.our-program .section-title h2,
.our-program .section-title p{
	color: var(--white-color);
}

.program-item{
	position: relative;
	height: calc(100% - 30px);
	background: var(--white-color);
	border: 1px solid var(--divider-color);
	box-shadow: 0px 0px 40px 5px #00000005;
	border-radius: 30px;
	padding: 40px;
	margin-bottom: 30px;
	z-index: 1;
}

.program-image{
	margin-bottom: 30px;
}

.program-image a{
	display: block;
	cursor: none;
}

.program-image figure{
	display: block;
	border-radius: 20px;
}

.program-image img{
	width: 100%;
	aspect-ratio: 1 / 0.701;
	object-fit: cover;
	border-radius: 20px;
	transition: all 0.4s ease-in-out;
}

.program-item:hover .program-image img{
	transform: scale(1.1);
}

.program-body{
	text-align: center;
}

.program-content{
	margin-bottom: 30px;
}

.program-content h3{
	font-size: 20px;
	margin-bottom: 10px;
}

.program-content h3 a{
	color: inherit;
}

.program-content p{
	margin-bottom: 0;
}

.our-program .section-footer-text{
	position: relative;
	width: 100%;
	max-width: 480px;
	margin: 30px auto 0;
	z-index: 1;
}

/************************************/
/***   11. Scrolling Ticker css   ***/
/************************************/

.scrolling-ticker-box{
	--gap: 40px;
	display: flex;
	overflow: hidden;
	user-select: none;
	gap: var(--gap);
	align-items: center;
}

.scrolling-content{
	flex-shrink: 0;
	display: flex;
	gap: var(--gap);
	min-width: 100%;
	animation: scroll 50s linear infinite;
}

@keyframes scroll{
	from{
		transform: translateX(0);
	}

	to{
		transform: translateX(calc(-100% - var(--gap)));
	}
}

.scrolling-ticker-box .scrolling-content span{
	display: flex;
	align-items: center;
	text-transform: capitalize;
	font-family: var(--accent-font);
	font-size: 100px;
	line-height: 1.2em;
	font-weight: 700;
	color: var(--white-color);
    background: var(--accent-color);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-stroke: 4px transparent;
}

.scrolling-ticker-box .scrolling-content span img{
	width: 44px;
	margin-right: 40px;
}

/************************************/
/***     12. Our Features css     ***/
/************************************/

.our-features{
	padding: 100px 0;
}

.our-features-list{
	display: flex;
	flex-wrap: wrap;
	gap: 30px 60px;
}

.our-features-item{
	width: calc(33.33% - 40px);
	display: flex;
	flex-wrap: wrap;
	gap: 60px;
}

.our-features-item:nth-child(even){
	flex-direction: column-reverse;
}

.our-features-image figure{
	display: block;
	border-radius: 30px;
}

.our-features-image img{
	width: 100%;
    aspect-ratio: 1 / 0.84;
	object-fit: cover;
	border-radius: 30px;
}

.our-features-content{
	display: flex;
	align-items: start;
	justify-content: space-between;
	gap: 15px;
}

.our-features-body{
	width: calc(100% - 87px);
}

.our-features-body h2{
	color: var(--accent-color);
	font-size: 50px;
	margin-bottom: 25px;
}

.our-features-body h3{
	font-size: 20px;
	font-weight: 600;
	text-transform: capitalize;
	margin-bottom: 10px;
}

.our-features-body p{
	margin: 0;
}

.our-features-content .icon-box{
	position: relative;
	display: inline-block;
	padding-bottom: 7px;
}

.our-features-content .icon-box:before{
	content: '';
	position: absolute;
	left: 50%;
	bottom: 0px;
	transform: translateX(-50%);
	background-color: var(--accent-color);
	border-radius: 50%;
	width: 43px;
	height: 43px;
	z-index: 0;
}

.our-features-content .icon-box img{
	position: relative;
	width: 100%;
	max-width: 72px;
	z-index: 1;
}

/************************************/
/***      13. Donate Now css      ***/
/************************************/

.donate-now{
	position: relative;
	background: url('../images/bg/donate-now-bg-image.jpg') no-repeat;
	background-position: center center;
	background-size: cover;
	padding: 150px 0;
}

.donate-now::before{
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background: var(--primary-color);
	opacity: 30%;
	height: 100%;
	width: 100%;
}

.intro-video-box{
	text-align: center;
	margin-right: 30px;
}

.intro-video-box .video-play-button{
	position: relative;
	display: inline-block;
	z-index: 1;
}

.intro-video-box .video-play-button a{
	width: 74px;
	height: 74px;
	margin: 0;
}

.intro-video-box .video-play-button a:before{
	content: '';
	position: absolute;
	top: -30%;
	left: -30%;
	width: 160%;
	height: 160%;
	border: 50px solid var(--white-color);
	opacity: 30%;
	border-radius: 50%;
	transform: scale(0.6);
	z-index: -1;
	animation: border-zooming 1.2s infinite linear;
}

.intro-video-box .video-play-button a:after{
	content: '';
	position: absolute;
	top: -30%;
	left: -30%;
	width: 160%;
	height: 160%;
	border: 50px solid var(--white-color);
	opacity: 30%;
	border-radius: 50%;
	transform: scale(0.6);
	z-index: -1;
	animation: border-zooming 1.2s infinite linear;
	animation-delay: .3s;
}

@keyframes border-zooming{
	100%{
		transform: scale(1);
		opacity: 0;
	}
}

.intro-video-box .video-play-button a i{
	font-size: 30px;
}

.donar-company-slider{
	margin-top: 120px;
}

.donar-company-slider .swiper-wrapper{
	cursor: none;
	align-items: center;
}

.donar-company-logo img{
	width: 100%;
	max-width: 200px;
	max-height: 40px;
}

.donate-box{
	position: relative;
	background-color: var(--white-color);
	border-radius: 30px;
	padding: 40px;
	z-index: 1;
}

.donate-value-box{
	display: flex;
	flex-wrap: wrap;
	gap: 10px;
	margin-bottom: 30px;
}

.donate-form .donate-value{
	width: calc(33.33% - 6.66px);
}

.donate-form .form-control{
	font-size: 16px;
	line-height: 1.5em;
	font-weight: 700;
	background-color: transparent;
	color: var(--primary-color);
	border: 1px solid var(--divider-color);
	border-radius: 10px;
	box-shadow: none;
	outline: none;
	padding: 17px 30px;
}

.donate-form .form-control::placeholder{
	color: var(--primary-color);
	font-weight: 700;
}

.donate-value-box .donate-value input{
	position: absolute;
	left: -9999px;
}

.donate-value-box .donate-value label{
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	font-weight: 400;
	line-height: 1.2em;
	background-color: var(--secondary-color);
	color: var(--primary-color);
	border-radius: 10px;
	padding: 20px;
	transition: all 0.3s ease-in-out;
	cursor: pointer;
	overflow: hidden;
}

.donate-value-box .donate-value input[type="radio"]:hover+label,
.donate-value-box .donate-value input[type="radio"]:focus+label,
.donate-value-box .donate-value input[type="radio"]:checked+label{
	background-color: var(--accent-color);
	color: var(--white-color);
}

.donate-box .donate-form .form-group-btn .btn-default{
	width: 100%;
	display: block;
	text-align: center;
	padding: 20px;
}

.donate-box .donate-form .form-group-btn .btn-default::before{
	display: none;
}

/************************************/
/***      14. How It Work css     ***/
/************************************/

.how-it-work{
	padding: 100px 0;
}

.how-it-work-list{
	display: flex;
	flex-wrap: wrap;
	gap: 30px;
}

.how-it-work-item{
	width: calc(25% - 22.5px);
	display: flex;
	flex-wrap: wrap;
	gap: 30px;
}

.how-it-work-item:nth-child(even){
	flex-direction: column-reverse;
}

.how-it-work-image,
.how-it-work-content{
	width: 100%;
}

.how-it-work-image figure{
	display: block;
	border-radius: 30px;
}

.how-it-work-image img{
	width: 100%;
	aspect-ratio: 1 / 0.866;
	object-fit: cover;
	border-radius: 30px;
}

.how-it-work-content{
	border: 1px solid var(--divider-color);
	box-shadow: 0px -10px 40px 5px #00000005;
	border-radius: 30px;
	text-align: center;
	padding: 20px;
}

.how-it-work-content .icon-box{
	position: relative;
	padding-bottom: 7px;
	margin-bottom: 30px;
	z-index: 1;
}

.how-it-work-content .icon-box:before{
	content: '';
	position: absolute;
	left: 50%;
	bottom: 0px;
	transform: translateX(-50%);
	background-color: var(--accent-color);
	border-radius: 50%;
	width: 34px;
	height: 34px;
	z-index: 0;
}

.how-it-work-content .icon-box img{
	position: relative;
	width: 100%;
	max-width: 55px;
	z-index: 1;
}

.how-it-work-body h3{
	font-size: 20px;
	font-weight: 600;
	text-transform: capitalize;
	margin-bottom: 10px;
}

.how-it-work-body p{
	margin: 0;
}

.section-footer-text.how-work-footer-text{
	margin-top: 60px;
}

/************************************/
/***   15. Our Testimonials css   ***/
/************************************/

.our-testimonials{
	background: url('../images/bg/testimonial-bg-image.png'), var(--secondary-color);
	background-repeat: no-repeat;
	background-position: right center;
	background-size: auto;
	padding: 100px 0;
}

.testimonials-image{
	position: relative;
	padding-right: 90px;
}

.testimonials-img figure{
	display: block;
	border-radius: 30px;
}

.testimonials-img img{
	width: 100%;
    aspect-ratio: 1 / 1.344;
	object-fit: cover;
	border-radius: 30px;
}

.helthcare-support-circle{
	position: absolute;
	top: 60px;
	right: 0;
	z-index: 1;
}

.helthcare-support-circle a{
	display: inline-block;
}

.helthcare-support-circle img{
	width: 100%;
	max-width: 180px;
	animation: infiniterotate 20s infinite linear;
}

@keyframes infiniterotate{
	from{
		transform: rotate(0deg);
	  }
	to{
		transform: rotate(360deg);
	}
}

.client-review-box{
	position: absolute;
	right: 0;
	bottom: 40px;
	background: var(--white-color);
	border-radius: 10px;
	padding: 20px;
	box-shadow: 0px 0px 20px 0px #0000000D;
	overflow: hidden;
	animation: clientreviewobject 3s infinite linear alternate;
	z-index: 1;
}

@keyframes clientreviewobject{
	50%{
		right: 45px;
	}
}

.client-review-box:before{
	content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 0;
    background: var(--accent-color);
    z-index: 0;
    transition: all 0.4s ease-in-out;
}

.client-review-box:hover:before{
	top: auto;
    height: 100%;
}

.client-review-box h2{
	position: relative;
	color: var(--accent-color);
	font-size: 50px;
	transition: all 0.4s ease-in-out;
	z-index: 1;
}

.client-review-box p{
	position: relative;
	text-transform: uppercase;
	margin: 0;
	transition: all 0.4s ease-in-out;
	z-index: 1;
}

.client-review-box:hover h2,
.client-review-box:hover p{
	color: var(--white-color);
}

.testimonials-content{
	margin-left: 20px;
}

.testimonial-slider .swiper-wrapper{
	cursor: none;
}

.testimonial-item{
	background-color: var(--white-color);
	box-shadow: 0px 0px 40px 5px #00000005;
	border: 1px solid var(--divider-color);
	border-radius: 30px;
	padding: 40px;
}

.testimonial-header{
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	justify-content: space-between;
	gap: 20px;
	border-bottom: 1px solid var(--divider-color);
	margin-bottom: 30px;
	padding-bottom: 30px;
}

.author-info{
	display: flex;
	align-items: center;
}

.author-image{
	margin-right: 20px;
}

.author-image figure{
	display: block;
	border-radius: 50%;
}

.author-image img{
	max-width: 60px;
	border-radius: 50%;
}

.author-content{
	width: calc(100% - 80px);
}

.author-content h3{
	font-size: 20px;
	font-weight: 600;
	text-transform: capitalize;
	margin-bottom: 5px;
}

.author-content p{
	text-transform: capitalize;
	margin: 0;
}

.testimonial-rating{
	text-align: right;
}

.testimonial-rating i{
	color: var(--accent-color);
	font-size: 16px;
}

.testimonial-content p{
	margin: 0;
}

.testimonial-slider .testimonial-pagination{
    text-align: center;
	margin-top: 40px;
	padding-bottom: 4px;
}

.testimonial-slider .testimonial-pagination .swiper-pagination-bullet{
	position: relative;
    height: 12px;
    width: 12px;
    background: var(--divider-color);
    opacity: 1;
    margin: 0 8px;
	transition: all 0.4s ease-in-out;
}

.testimonial-slider .testimonial-pagination .swiper-pagination-bullet:before{
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border: 1px solid transparent;
    height: 0;
    width: 0;
    border-radius: 50%;
	transition: all 0.4s ease-in-out;
}

.testimonial-slider .testimonial-pagination .swiper-pagination-bullet-active:before{
	border-color: var(--accent-color);
	height: 24px;
	width: 24px;
}

.testimonial-slider .testimonial-pagination .swiper-pagination-bullet-active{
	background: var(--accent-color);
}

/************************************/
/***      16. Our Gallery css     ***/
/************************************/

.our-gallery{
	padding: 100px 0 50px;
}

.our-gallery .container-fluid{
	padding: 0;
}

.our-gallery-nav{
	text-align: center;
	margin-bottom: 80px;
}

.our-gallery-nav ul{
	list-style: none;
	text-align: center;
	display: inline-flex;
	flex-wrap: wrap;
	align-items: center;
	justify-content: center;
	gap: 10px 60px;
	padding: 0;margin: 0;
}

.our-gallery-nav ul li a{
	position: relative;
	display: inline-block;
	color: var(--text-color);
	font-weight: 600;
	line-height: 1.2em;
	text-transform: capitalize;
	transition: all 0.3s ease-in-out;
}

.our-gallery-nav ul li a:before{
    content: '';
    position: absolute;
    top: 5px;
    right: -34px;
	background-color: var(--divider-color);
	border-radius: 50%;
	height: 8px;
	width: 8px;
}

.our-gallery-nav ul li:last-child a:before{
	display: none;
}

.our-gallery-nav ul li a.active-btn,
.our-gallery-nav ul li a:hover{
	color: var(--accent-color);
}

.gallery-item-boxes{
	display: flex;
	flex-wrap: wrap;
	left: 1px;
}

.gallery-item-box{
	width: calc(20% - 0.2px);
}

.gallery-item-box figure{
	display: block;
	width: 100%;
}

.gallery-item-box img{
	width: 100%;
	aspect-ratio: 1 / 0.921;
	object-fit: cover;
}

/************************************/
/***       17. Our Blog css       ***/
/************************************/

.our-blog{
	padding: 50px 0 70px;
}

.post-item{
	background-color: var(--white-color);
	border: 1px solid var(--divider-color);
	border-radius: 30px;
	box-shadow: 0px 0px 40px 5px #00000005;
	padding: 40px;
	height: calc(100% - 30px);
	margin-bottom: 30px;
}

.post-item-header{
	margin-bottom: 30px;
}

.post-item-meta{
	margin-bottom: 15px;
}

.post-item-meta ul{
	list-style: none;
	margin: 0;
	padding: 0;
} 

.post-item-meta ul li{
	color: var(--accent-color);
	text-transform: capitalize;
}

.post-item-content h2{
	font-size: 20px;
	line-height: 1.3em;
}

.post-item-content h2 a{
	color: inherit;
}

.post-featured-image{
	margin-bottom: 30px;
}

.post-featured-image a{
    display: block;
	border-radius: 20px;
	overflow: hidden;
	cursor: none;
}

.post-featured-image img{
    width: 100%;
    aspect-ratio: 1 / 0.74;
    object-fit: cover;
    transition: all 0.4s ease-in-out;
}

.post-item:hover .post-featured-image img{
    transform: scale(1.1);
}

/************************************/
/***        18. Footer css        ***/
/************************************/

.main-footer{
    background: url(../images/bg/footer-bg.png), var(--primary-color);
	background-repeat: no-repeat;
    background-position: bottom center;
    background-size: auto;
    padding: 100px 0 0;
}

.main-footer-box{
	display: flex;
	flex-wrap: wrap;
	gap: 40px 100px;
}

.footer-about{
	position: relative;
	width: calc(45% - 50px);
}

.footer-about::before{
	content: '';
	position: absolute;
	top: 0;
	right: -50px;
	bottom: 0;
	background-color: var(--dark-divider-color);
	height: 100%;
	width: 1px;
}

.footer-logo{
	margin-bottom: 50px;
}

.footer-logo img{
	width: 100%;
	max-width: 161px;
}

.footer-contact-detail{
	display: flex;
	flex-wrap: wrap;
	gap: 20px 30px;
}

.footer-contact-item{
	width: calc(50% - 15px);
}

.footer-contact-item p{
	color: var(--white-color);
	opacity: 80%;
	margin-bottom: 15px;
}

.footer-contact-item h3{
	font-size: 17px;
	font-weight: 600;
	color: var(--white-color);
}

.footer-contact-item h3 a{
	color: inherit;
	transition: all 0.4s ease-in-out;
}

.footer-contact-item h3 a:hover{
	color: var(--accent-color);
}

.footer-social-links{
	margin-top: 50px;
}

.footer-social-links h3{
	font-size: 20px;
	font-weight: 600;
	color: var(--white-color);
	text-transform: capitalize;
	margin-bottom: 15px;
}

.footer-social-links ul{
    margin: 0;
    padding: 0;
    list-style: none;
}

.footer-social-links ul li{
	display: inline-block;
	margin-right: 10px;
}

.footer-social-links ul li:last-child{
	margin-right: 0;
}

.footer-social-links ul li a{
	width: 36px;
	height: 36px;
	color: var(--white-color);
	background: var(--accent-color);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.4s ease-in-out;
}

.footer-social-links ul li a:hover{
	color: var(--primary-color);
	background: var(--white-color);
}

.footer-social-links ul li a i{
	font-size: 18px;
	color: inherit;
}

.footer-links-box{
	width: calc(55% - 50px);
	display: flex;
	flex-wrap: wrap;
	gap: 50px;
}

.newsletter-form{
	width: 100%;
}

.newsletter-form .form-group{
	display: flex;
}

.newsletter-form .form-group .form-control{
	width: calc(100% - 56px);
	font-size: 16px;
	font-weight: 400;
	line-height: 1.4em;
	color: var(--text-color);
	background: transparent;
	border: 1px solid var(--dark-divider-color);
	border-radius: 100px;
	outline: none;
	box-shadow: none;
	padding: 15px 20px;
}

.newsletter-form .form-group .form-control::placeholder{
	color: var(--text-color);
}

.newsletter-form .form-group .newsletter-btn{
	background-color: var(--accent-color);
	width: 56px;
	height: 56px;
	border: none;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0;
	transition: all 0.3s ease-in-out;
	color: var(--white-color);
}

.newsletter-form .form-group .newsletter-btn:hover{
	background-color: var(--white-color);
	color: var(--primary-color);
}

.newsletter-form .form-group .newsletter-btn i{
	font-size: 24px;
	padding: 0 2px 2px 0;
	transition: all 0.4s ease-in-out;
}

.newsletter-form .form-group .newsletter-btn:hover i{
	transform: rotate(25deg);
}

.footer-links{
	width: calc(30% - 33.33px);
}

.footer-links.footer-service-links{
	width: calc(40% - 33.33px);
}

.footer-links h3{
	font-size: 20px;
	font-weight: 600;
	color: var(--white-color);
	text-transform: capitalize;
	margin-bottom: 30px;
}

.footer-links ul{
	margin: 0;
	padding: 0;
	list-style: none;
}

.footer-links ul li{
	color: var(--white-color);
	text-transform: capitalize;
	line-height: 1.6em;
	margin-bottom: 15px;
	transition: all 0.3s ease-in-out;
}

.footer-links ul li:hover{
	color: var(--accent-color);
}

.footer-links ul li:last-child{
	margin-bottom: 0;
}

.footer-links ul li a{
	color: inherit;
}

.footer-copyright{
	background-color: var(--dark-divider-color);
	padding: 20px 0;
	margin-top: 80px;
}

.copyright-text{
	text-align: center;
}

.copyright-text p{
	color: var(--white-color);
	opacity: 80%;
	margin-bottom: 0;
}

/************************************/
/***     19. About Us Page css    ***/
/************************************/

.page-header{
   position: relative;
	background: url('../images/bg/page-header-bg.jpg');
	background-repeat: no-repeat;
	background-position: center center;
	background-size: cover;
	padding: 235px 0 135px;
}

.page-header::before{
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(270deg, rgba(2, 13, 25, 0) 23.01%, rgba(2, 13, 25, 0.8) 92.29%),linear-gradient(360deg, rgba(2, 13, 25, 0) 70.82%, #020D19 101.14%);
	height: 100%;
	width: 100%;
	z-index: 1;
}

.page-header-box{
	position: relative;
	z-index: 1;
}

.page-header-box h1{
	display: inline-block;
    font-size: 63px;
    font-weight: 700;
    line-height: 1.2em;
    color: var(--white-color);
    margin-bottom: 10px;
    cursor: none;
}

.page-header-box h1 span{
	font-weight: 700;
    color: var(--accent-color);
}

.page-header-box ol{
	margin: 0;
	padding: 0;
	align-items: center;
}

.page-header-box ol li.breadcrumb-item{
	font-size: 16px;
    font-weight: 500;
	text-transform: capitalize;
	color: var(--white-color);
}

.page-header-box ol li.breadcrumb-item.active{
	color: var(--accent-color);
}

.page-header-box ol li.breadcrumb-item a{
    color: inherit;
}

.page-header-box ol .breadcrumb-item+.breadcrumb-item::before{
	content: "\f111";
    font-family: "FontAwesome";
	font-size: 6px;
    color: var(--white-color);
}

.our-approach{
	padding: 100px 0;
	background: url('../images/bg/our-service-bg.svg'), var(--secondary-color);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: auto;
}

.our-approach-box-content{
	position: relative;
	display: flex;
	flex-wrap: wrap;
	gap: 30px;
	padding-bottom: 100px;
}

.our-approach-content{
	width: calc(50% - 15px);
	padding-bottom: 200px;
}

.our-approach-image{
	width: calc(50% - 15px);
}

.our-approach-image figure{
	display: block;
	border-radius: 30px;
}

.our-approach-image img{
	width: 100%;
    aspect-ratio: 1 / 0.947;
	object-fit: cover;
	border-radius: 30px;
}

.mission-vision-box{
	position: absolute;
	left: 0;
	max-width: 970px;
	display: flex;
	flex-wrap: wrap;
	gap: 30px 80px;
	background-color: var(--white-color);
	border: 1px solid var(--divider-color);
	border-radius: 30px;
	box-shadow: 0px -10px 40px 5px #00000005;
	padding: 50px;
	overflow: hidden;
	z-index: 2;
}

.mission-vision-item{
	position: relative;
    width: calc(33.33% - 53.33px);
}

.mission-vision-item:before{
	content: '';
    position: absolute;
    top: 50%;
	transform: translateY(-50%);
    bottom: 0;
    right: -40px;
    height: calc(100% - 30px);
    width: 1px;
    background: var(--divider-color);
}

.mission-vision-item:nth-child(3n + 3):before{
	display: none;
}

.mission-vision-item .icon-box{
	position: relative;
	display: inline-block;
	padding-bottom: 5px;
	margin-bottom: 20px;
}

.mission-vision-item .icon-box:before{
	content: '';
	position: absolute;
	left: 50%;
	bottom: 0px;
	transform: translateX(-50%);
	background-color: var(--accent-color);
	border-radius: 50%;
	width: 30px;
	height: 30px;
	z-index: 0;
}

.mission-vision-item .icon-box img{
	position: relative;
	width: 100%;
	max-width: 50px;
	z-index: 1;
}

.mission-vision-content h3{
	font-size: 20px;
	font-weight: 600;
	text-transform: capitalize;
	margin-bottom: 10px;
}

.mission-vision-content p{
	margin: 0;
}

.how-we-help{
	position: relative;
	padding: 100px 0;
}

.how-we-help:before{
	content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-color);
	border-radius: 0 30px 30px 0;
    height: 100%;
    width: 55%;
    z-index: -1;
}

.how-we-help-content .section-title h3,
.how-we-help-content .section-title h2,
.how-we-help-content .section-title p{
	color: var(--white-color);
}

.how-we-help-body{
	margin-bottom: 40px;
}

.how-we-help-body ul{
	list-style: none;
	margin: 0;
	padding: 0;
}

.how-we-help-body ul li{
	background: url('../images/icon/icon-check.svg') no-repeat;
    background-position: left center;
    background-size: 26px auto;
	color: var(--white-color);
	line-height: 1.5em;
	padding-left: 30px;
	margin-bottom: 20px;
}

.how-we-help-body ul li:last-child{
	margin-bottom: 0;
}

.how-we-help-btn .btn-default:after{
	background: var(--white-color);
}

.how-we-help-btn .btn-default:hover:before{
	filter: brightness(0) invert(0);
}

.how-we-help-btn .btn-default:hover{
	color: var(--primary-color);
}

.how-help-list{
	display: flex;
	flex-wrap: wrap;
	gap: 30px;
}

.how-help-item{
	width: calc(50% - 15px);
	background-color: var(--white-color);
	border: 1px solid var(--divider-color);
	box-shadow: 0px -10px 40px 5px #00000005;
	border-radius: 30px;
	padding: 20px;
	text-align: center;
}

.how-help-item .icon-box{
	position: relative;
	display: inline-block;
	padding-bottom: 5px;
	margin-bottom: 30px;
}

.how-help-item .icon-box:before{
	content: '';
	position: absolute;
	left: 50%;
	bottom: 0px;
	transform: translateX(-50%);
	background-color: var(--accent-color);
	border-radius: 50%;
	width: 33px;
	height: 33px;
	z-index: 0;
}

.how-help-item .icon-box img{
	position: relative;
	width: 100%;
	max-width: 55px;
	z-index: 1;
}

.how-help-item-content h3{
	font-size: 20px;
	font-weight: 600;
	text-transform: capitalize;
	margin-bottom: 10px;
}

.how-help-item-content p{
	margin: 0;
}

.our-fact{
	background-color: var(--secondary-color);
	padding: 100px 0;
}

.our-fact-image figure{
	display: block;
	border-radius: 30px;
}

.our-fact-image img{
	width: 100%;
    aspect-ratio: 1 / 0.947;
	object-fit: cover;
	border-radius: 30px;
}

.our-fact-body{
	display: flex;
	flex-wrap: wrap;
	gap: 30px;
}

.fact-counter-list,
.fact-body-image{
	width: calc(50% - 15px);
}

.fact-counter-item{
	position: relative;
	display: flex;
	align-items: center;
	gap: 10px;
	background-color: var(--white-color);
	border-radius: 20px;
	padding: 35px 20px;
	margin-bottom: 30px;
	overflow: hidden;
}

.fact-counter-item:before{
	content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 0;
    background: var(--accent-color);
    z-index: 0;
    transition: all 0.4s ease-in-out;
}

.fact-counter-item:hover:before{
	top: auto;
    height: 100%;
}

.fact-counter-item:last-child{
	margin-bottom: 0;
}

.fact-counter-item h2{
	position: relative;
	width: calc(58% - 5px);
	font-size: 60px;
	line-height: 1em;
	transition: all 0.4s ease-in-out;
	z-index: 1;
}

.fact-counter-item:hover h2{
	color: var(--white-color);
}

.fact-counter-item p{
	position: relative;
	width: calc(42% - 5px);
	text-transform: capitalize;
	transition: all 0.4s ease-in-out;
	margin: 0;
	z-index: 1;
}

.fact-counter-item:hover p{
	color: var(--white-color);
}

.fact-body-image figure{
	display: block;
	height: 100%;
	border-radius: 30px;
}

.fact-body-image img{
	width: 100%;
	height: 100%;
	aspect-ratio: 1 / 0.98;
	object-fit: cover;
	border-radius: 30px;
}

.our-team{
	padding: 100px 0 70px;
}

.team-item{
	background-color: var(--white-color);
	border: 1px solid var(--divider-color);
	box-shadow: 0px -10px 40px 5px #00000008;
	border-radius: 30px;
	height: calc(100% - 30px);
	margin-bottom: 30px;
	padding: 20px;
}

.team-image{
	margin-bottom: 20px;
}

.team-image a,
.team-image a figure{
	display: block;
	border-radius: 20px;
	cursor: none;
}

.team-image a img{
	width: 100%;
	aspect-ratio: 1 / 0.87;
	object-fit: cover;
	transition: all 0.4s ease-in-out;
}

.team-item:hover .team-image a img{
	transform: scale(1.1);
}

.team-content{
	text-align: center;
	margin-bottom: 15px;
}

.team-content h3{
	font-size: 20px;
	text-transform: capitalize;
	margin-bottom: 5px;
}

.team-content h3 a{
	color: inherit;
}

.team-content p{
	text-transform: capitalize;
	margin-bottom: 0;
}

.team-social-icon ul{
	list-style: none;
	margin: 0;
	padding: 0;
	text-align: center;
}

.team-social-icon ul li{
	display: inline-block;
	margin-right: 10px;
}

.team-social-icon ul li:last-child{
	margin-right: 0;
}

.team-social-icon ul li a{
	width: 36px;
	height: 36px;
	color: var(--white-color);
	background: var(--accent-color);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.4s ease-in-out;
}

.team-social-icon ul li a:hover{
	background: var(--primary-color);
}

.team-social-icon ul li a i{
	color: inherit;
	font-size: 18px;
}

.our-faqs{
	padding: 100px 0;
}

.faq-accordion .accordion-item{
	background: var(--white-color);
    border: 1px solid var(--divider-color);
	border-radius: 10px;
	margin-bottom: 30px;
    padding: 0;
	transition: all 0.3s ease-in-out;
	overflow: hidden;
}

.faq-accordion .accordion-item:last-child{
	margin-bottom: 0;
}

.faq-accordion .accordion-header .accordion-button{
	font-size: 20px;
	font-weight: 600;
	line-height: 1.2em;
	background: var(--accent-color);
	color: var(--white-color);
	padding: 18px 40px 18px 20px;
	transition: all 0.3s ease-in-out;
}

.faq-accordion .accordion-header .accordion-button.collapsed{
	background: var(--white-color);
	color: var(--primary-color);
}

.faq-accordion .accordion-item .accordion-button::after,
.faq-accordion .accordion-item .accordion-button.collapsed::after{
	content: '\f107';
	font-family: "FontAwesome";
	position: absolute;
	right: 20px;
	top: 50%;
	bottom: auto;
	transform: translateY(-50%);
	font-size: 18px;
	font-weight: 900;
    line-height: normal;
    color: var(--white-color);
	transition: all 0.3s ease-in-out;
}

.faq-accordion .accordion-item .accordion-button.collapsed::after{
	transform: translateY(-50%) rotate(-90deg);
	color: var(--primary-color);
}

.faq-accordion .accordion-item .accordion-body{
	padding: 18px 40px 18px 20px;
}

.faq-accordion .accordion-item .accordion-body p:last-child{
	margin: 0;
}

.faqs-image{
	position: relative;
	padding-right: 145px;
	margin-left: 30px;
}

.faqs-image:before{
	content: '';
	position: absolute;
	right: 80px;
	bottom: 40px;
	width: 30px;
	height: 185px;
	background-color: var(--accent-color);
	border-radius: 100px;
}

.faqs-img-1,
.faqs-img-2{
	border-radius: 30px;
	overflow: hidden;
} 

.faqs-img-1 figure,
.faqs-img-2 figure{
	display: block;
}

.faqs-img-1 img,
.faqs-img-2 img{
	width: 100%;
	object-fit: cover;
}

.faqs-img-2{
	max-width: 302px;
	position: absolute;
	top: 140px;
	right: 0;
	border: 8px solid var(--white-color);
	border-radius: 30px;
	z-index: 1;
}

.faqs-img-1 img{
	aspect-ratio: 1 / 1.614;
}

.faqs-img-2 img{
	aspect-ratio: 1 / 1.1;
}

/************************************/
/***     20. Services Page css    ***/
/************************************/

.page-services{
	padding: 100px 0 70px;
}

.why-choose-us.service-why-choose-us{
	background-color: var(--secondary-color);
}

/************************************/
/***    21. Service Single css    ***/
/************************************/

.page-service-single{
	padding: 100px 0;
}

.page-single-sidebar{
    position: sticky;
    top: 30px;
    margin-right: 20px;
}

.page-sidebar-catagery-list{
	border: 1px solid var(--divider-color);
    border-radius: 30px;
    margin-bottom: 60px;
	box-shadow: 0px -10px 40px 5px #00000005;
	overflow: hidden;
}

.page-sidebar-catagery-list h3{
	font-size: 20px;
    font-weight: 600;
    text-transform: capitalize;
    background-color: var(--accent-color);
    color: var(--white-color);
    padding: 20px 30px;
}

.page-sidebar-catagery-list ul{
    list-style: none;
    margin: 0;
    padding: 30px;
}

.page-sidebar-catagery-list ul li{
	line-height: 1.5em;
    border-bottom: 1px solid var(--divider-color);
    padding-bottom: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease-in-out;
}

.page-sidebar-catagery-list ul li:last-child{
    margin: 0;
    padding: 0;
    border-bottom: none;
}

.page-sidebar-catagery-list ul li a{
    position: relative;
	display: block;
    text-transform: capitalize;
    color: var(--text-color);
	padding-right: 30px;
    transition: all 0.3s ease-in-out;
}

.page-sidebar-catagery-list ul li a:hover{
    color: var(--primary-color);
}

.page-sidebar-catagery-list ul li a::before{
	content: '';
	position: absolute;
	top: 50%;
	right: 0;
	background: url(../images/arrow-text.svg) no-repeat;
	background-size: cover;
	width: 20px;
	height: 20px;
    transform: translateY(-50%);
	transition: all 0.3s ease-in-out;
}

.page-sidebar-catagery-list ul li a:hover:before{
	filter: brightness(0) invert(0);
}

.sidebar-cta-box{
	position: relative;
	background-image: url(../images/bg/sidebar-cta-bg-img.jpg);
	background-repeat: no-repeat;
	background-position: center center;
	background-size: cover;
    border-radius: 30px;
	text-align: center;
	padding: 40px;
    overflow: hidden;
	z-index: 1;
}

.sidebar-cta-box::before{
	content: '';
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: var(--primary-color);
	opacity: 60%;
	height: 100%;
	width: 100%;
	z-index: 0;
}

.sidebar-cta-box .icon-box,
.sidebar-cta-content{
	position: relative;
	margin-bottom: 40px;
	z-index: 1;
}

.sidebar-cta-box .icon-box img{
	width: 100%;
	max-width: 92px;
}

.sidebar-cta-content p{
	color: var(--white-color);
	text-transform: capitalize;
	margin-bottom: 15px;
}

.sidebar-cta-content h3{
	font-size: 20px;
	font-weight: 600;
	text-transform: capitalize;
	color: var(--white-color);
}

.service-feature-image{
	margin-bottom: 40px;
}

.service-feature-image figure{
	display: block;
	border-radius: 30px;
}

.service-feature-image img{
	width: 100%;
	object-fit: cover;
	aspect-ratio: 1 / 0.598;
	border-radius: 30px;
}

.service-entry{
	margin-bottom: 60px;
}

.service-entry p{
	margin-bottom: 20px;
}

.service-entry p:last-child{
	margin-bottom: 0;
}

.service-entry h2{
	font-size: 50px;
	margin-bottom: 20px;
}

.service-entry ul{
	display: flex;
	gap: 20px 30px;
	flex-wrap: wrap;
	background-color: var(--secondary-color);
	border-radius: 30px;
    list-style: none;
	padding: 40px;
    margin: 40px 0;
}

.service-entry ul li{
	background: url(../images/icon/icon-check-dark.svg) no-repeat center left;
    background-size: 26px auto;
	width: calc(33.33% - 20px);
	line-height: 1.5em;
    text-transform: capitalize;
    color: var(--primary-color);
	padding-left: 30px;
}

.bringing-quality-box,
.service-entry-content-list,
.service-entry-steps{
	margin-top: 60px;
}

.service-entry-content-list{
	display: flex;
	flex-wrap: wrap;
	gap: 30px;
}

.service-entry-content-item{
	width: 100%;
	display: flex;
	align-items: center;
	gap: 30px;
	flex-wrap: wrap;
}

.service-entry-content-item:nth-child(even){
	flex-direction: row-reverse;
}

.service-entry-image,
.service-entry-content-box{
	width: calc(50% - 15px);
}

.service-entry-image figure{
	display: block;
	border-radius: 30px;
}

.service-entry-image img{
	width: 100%;
	border-radius: 30px;
	aspect-ratio: 1 / 0.66;
	border-radius: 30px;
}

.service-entry-content-box .icon-box{
	position: relative;
	display: inline-block;
	margin-bottom: 20px;
	padding-bottom: 5px;
}

.service-entry-content-box .icon-box:before{
	content: '';
	position: absolute;
	left: 50%;
	bottom: 0px;
	transform: translateX(-50%);
	background-color: var(--accent-color);
	border-radius: 50%;
	width: 30px;
	height: 30px;
	z-index: 0;
}

.service-entry-content-box .icon-box img{
	position: relative;
	width: 100%;
	max-width: 50px;
	z-index: 1;
}

.service-entry-content h3{
	font-size: 20px;
	font-weight: 600;
	text-transform: capitalize;
	margin-bottom: 10px;
}

.service-entry-step-list{
	margin-top: 40px;
}

.service-entry-step-item{
	position: relative;
	display: flex;
	align-items: center;
	background: var(--secondary-color);
	border-radius: 30px;
	padding: 30px;
	margin-bottom: 30px;
	transition: all 0.4s ease-in-out;
	overflow: hidden;
}

.service-entry-step-item::before{
    content: '';
    display: block;
    position: absolute;
    top: auto;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--accent-color);
    width: 100%;
    height: 0;
    transition: all 0.4s ease-in-out;
    z-index: 0;
}

.service-entry-step-item.active::before,
.service-entry-step-item:hover::before{
    height: 100%;
    top: 0;
    bottom: auto;
}

.service-entry-step-item:last-child{
	margin-bottom: 0;
}

.service-entry-step-box{
	position: relative;
	width: calc(100% - 70px);
	display: flex;
	z-index: 1;
}

.service-entry-step-no{
	margin-right: 30px;
}

.service-entry-step-no h2{
	margin-bottom: 0;
	transition: all 0.3s ease-in-out;
}

.service-entry-step-content h3{
	font-size: 20px;
	margin-bottom: 10px;
	transition: all 0.3s ease-in-out;
}

.service-entry-step-content p{
	margin-bottom: 0;
	transition: all 0.4s ease-in-out;
}

.service-entry-step-item.active .service-entry-step-content p,
.service-entry-step-item:hover .service-entry-step-content p,
.service-entry-step-item.active .service-entry-step-no h2,
.service-entry-step-item:hover .service-entry-step-no h2,
.service-entry-step-item.active .service-entry-step-content h3,
.service-entry-step-item:hover .service-entry-step-content h3{
	color: var(--white-color);
}

.service-entry-step-item .icon-box{
	position: relative;
	display: inline-block;
	margin-left: 15px;
	padding-bottom: 5px;
}

.service-entry-step-item .icon-box:before{
	content: '';
	position: absolute;
	left: 50%;
	bottom: 0px;
	transform: translateX(-50%);
	background: var(--accent-color);
	border-radius: 50%;
	width: 30px;
	height: 30px;
	z-index: 1;
	transition: all 0.3s ease-in-out;
}

.service-entry-step-item.active .icon-box:before,
.service-entry-step-item:hover .icon-box:before{
	background: var(--white-color);
	opacity: 50%;
}

.service-entry-step-item .icon-box img{
	position: relative;
	width: 100%;
	max-width: 55px;
	z-index: 1;
}

/************************************/
/***     22. Blog Archive css     ***/
/************************************/

.page-blog{
	padding: 100px 0;
}

.page-pagination{
    margin-top: 30px;
    text-align: center;
}

.page-pagination ul{
    justify-content: center;
    padding: 0;
    margin: 0;
}

.page-pagination ul li a,
.page-pagination ul li span{
    display: flex;
    text-decoration: none;
    justify-content: center;
    align-items: center;
    background: var(--secondary-color);
    color: var(--primary-color);
	border-radius: 10px;
    width: 40px;
    height: 40px;
    margin: 0 5px;
    font-weight: 600;
	line-height: 1em;
    transition: all 0.3s ease-in-out;
}

.page-pagination ul li.active a, 
.page-pagination ul li a:hover{
    background: var(--accent-color);
    color: var(--white-color);
}

/************************************/
/***      23. Blog Single css     ***/
/************************************/

.page-single-post{
	padding: 100px 0;
}

.post-image{
	position: relative;
	margin-bottom: 30px;
}

.post-image figure{
	display: block;	
	border-radius: 40px;
	overflow: hidden;
}

.post-image img{
	width: 100%;
	aspect-ratio: 1 / 0.50;
	object-fit: cover;
	border-radius: 40px;
}

.post-content{
	width: 100%;
	max-width: 1100px;
	margin: 0 auto;
}

.post-entry{
	border-bottom: 1px solid var(--divider-color);
	padding-bottom: 30px;
    margin-bottom: 30px;
}

.post-entry:after{
    content: '';
    display: block;
    clear: both;
}

.post-entry a{
    color: var(--accent-color);
}

.post-entry h1,
.post-entry h2,
.post-entry h3,
.post-entry h4,
.post-entry h5,
.post-entry h6{
	font-weight: 700;
	line-height: 1.2em;
	margin: 0 0 0.455em;
}

.post-entry h1{
	font-size: 63px;
}

.post-entry h2{
	font-size: 50px;
}

.post-entry h3{
	font-size: 40px;
}

.post-entry h4{
	font-size: 30px;
}

.post-entry h5{
	font-size: 24px;
}

.post-entry h6{
	font-size: 18px;
}

.post-entry p{
	margin-bottom: 20px;
}

.post-entry p:last-child{
	margin-bottom: 0;
}

.post-entry p strong{
	color: var(--primary-color);
	font-size: 20px;
	font-weight: 600; 
}

.post-entry ol{
    margin: 0 0 30px;
}

.post-entry ol li{
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: 600;
    color: var(--text-color);
}

.post-entry ul{
	padding: 0;
	margin: 20px 0 20px;
	padding-left: 20px;
}

.post-entry ul li{
    position: relative;
	font-size: 18px;
    font-weight: 500;
    line-height: 1.5em;
    color: var(--text-color);
    margin-bottom: 15px;
}

.post-entry ul li:last-child{
	margin-bottom: 0;
}

.post-entry ul ul,
.post-entry ul ol,
.post-entry ol ol,
.post-entry ol ul{
    margin-top: 20px;
    margin-bottom: 0;
}

.post-entry ul ul li:last-child,
.post-entry ul ol li:last-child,
.post-entry ol ol li:last-child,
.post-entry ol ul li:last-child{
    margin-bottom: 0;
}

.post-entry blockquote{
	background: url('../images/icon/icon-blockquote.svg'), var(--primary-color);
	background-repeat: no-repeat;
	background-position: 35px 25px;
    background-size: 55px;
	border-radius: 30px;
    padding: 30px 30px 30px 100px;
    margin-bottom: 30px;
}

.post-entry blockquote p{
	font-family: var(--accent-font);
	font-size: 22px;
	font-weight: 600;
	line-height: 1.4em;
	color: var(--white-color);
}

.post-entry blockquote p:last-child{
	margin-bottom: 0;
}

.tag-links{
    font-family: var(--accent-font);
    font-size: 22px;
	font-weight: 700;
    text-transform: capitalize;
	color: var(--primary-color);
	display: inline-flex;
	align-items: center;
	flex-wrap: wrap;
	gap: 10px;
}

.post-tags .tag-links a{
    display: inline-block;
    font-size: 16px;
    font-weight: 700;
    text-transform: capitalize;
    line-height: 1em;
	background: var(--accent-color);
    color: var(--white-color);
	border-radius: 10px;
    padding: 12px 20px;
	transition: all 0.3s ease-in-out;
}

.post-tags .tag-links a:hover{
	background: var(--primary-color);
}

.post-social-sharing{
    text-align: right;
}

.post-social-sharing ul{
    list-style: none;
    padding: 0;
    margin: 0;
}

.post-social-sharing ul li{
    display: inline-block;
    margin-right: 10px;
}

.post-social-sharing ul li:last-child{
	margin-right: 0;
}

.post-social-sharing ul li a{
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
	background: var(--accent-color);
    color: var(--white-color);
	border-radius: 10px;
    width: 38px;
    height: 38px;
    transition: all 0.3s ease-in-out;
}

.post-social-sharing ul li:hover a{
	background: var(--primary-color);
}

.post-social-sharing ul li a i{
    font-size: 18px;
    color: inherit;
}

/************************************/
/***     24. Projects Page css    ***/
/************************************/

.page-programs{
	padding: 100px 0 70px;
}

/************************************/
/***    25. Project Single css    ***/
/************************************/

.page-program-single{
	padding: 100px 0;
}

.program-feature-image{
	margin-bottom: 40px;
}

.program-feature-image figure{
	display: block;
	border-radius: 30px;
}

.program-feature-image img{
	width: 100%;
	aspect-ratio: 1 / 0.598;
	object-fit: cover;
	border-radius: 30px;
}

.program-entry{
	margin-bottom: 60px;
}

.program-entry h2{
	font-size: 50px;
	margin-bottom: 20px;
}

.program-entry p{
	margin-bottom: 20px;
}

.program-entry p:last-child{
	margin-bottom: 0;
}

.program-entry ul{
	display: flex;
	gap: 20px 30px;
	flex-wrap: wrap;
    list-style: none;
	padding: 0;
    margin: 40px 0 0 0;
}

.program-entry ul li{
	width: calc(50% - 15px);
	background: url(../images/icon/icon-check-dark.svg) no-repeat;
	background-position: center left;
    background-size: 26px auto;
	line-height: 1.5em;
    text-transform: capitalize;
    color: var(--primary-color);
	padding-left: 30px;
}

.building-stability-box,
.program-why-choose{
	margin-top: 60px;
}

.program-entry-video{
	display: flex;
	gap: 30px;
	flex-wrap: wrap;
	text-align: left;
	margin: 40px 0;
}

.program-entry-video-item{
	position: relative;
	width: calc(50% - 15px);
}

.program-entry-video-image figure{
	display: block;
	border-radius: 30px;
	overflow: hidden;
}

.program-entry-video-image figure::before{
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: var(--primary-color);
	opacity: 30%;
	z-index: 1;
}

.program-entry-video-image img{
	width: 100%;
	aspect-ratio: 1 / 0.87;
	object-fit: cover;
	border-radius: 30px;
}

.program-entry-video-item .video-play-button{
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

.program-entry-video.intro-video-box .video-play-button a:before,
.program-entry-video.intro-video-box .video-play-button a:after{
    top: -35%;
    left: -35%;
}

.program-entry-video-item .video-play-button a{
	height: 60px;
	width: 60px;
}

.program-entry-video-item .video-play-button a i{
    font-size: 26px;
}

.program-why-choose-list{
	display: flex;
	flex-wrap: wrap;
	gap: 30px;
}

.program-why-choose-item{
	width: calc(50% - 15px);
	border: 1px solid var(--divider-color);
	box-shadow: 0px -10px 40px 5px #00000005;
	border-radius: 30px;
	text-align: center;
	padding: 20px;
}

.program-why-choose-item .icon-box{
	position: relative;
	display: inline-block;
	padding-bottom: 5px;
	margin-bottom: 30px;
}

.program-why-choose-item .icon-box:before{
	content: '';
	position: absolute;
	left: 50%;
	bottom: 0px;
	transform: translateX(-50%);
	background-color: var(--accent-color);
	border-radius: 50%;
	width: 33px;
	height: 33px;
	z-index: 0;
}

.program-why-choose-item .icon-box img{
	position: relative;
	width: 100%;
	max-width: 55px;
	z-index: 1;
}

.program-why-choose-content h3{
	font-size: 20px;
	font-weight: 600;
	text-transform: capitalize;
	margin-bottom: 10px;
}

.program-why-choose-content p{
	margin: 0;
}

.section-footer-text.program-why-choose-footer{
	margin-top: 40px;
}

/************************************/
/***      26. Team Page css       ***/
/************************************/

.page-team{
	padding: 100px 0 70px;
}

/************************************/
/***      27. Team Single css     ***/
/************************************/

.page-team-single{
	padding: 100px 0;
}

.page-team-single-box{
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	gap: 100px 60px;
}

.team-member-info-box{
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	gap: 30px 60px;
}

.team-member-image,
.team-member-content{
	width: calc(50% - 30px);
}

.team-member-image figure{
	display: block;
	border-radius: 20px;
}

.team-member-image img{
	width: 100%;
    aspect-ratio: 1 / 1.06;
	object-fit: cover;
	border-radius: 20px;
}

.team-member-info{
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	justify-content: space-between;
	gap: 20px;
	margin-bottom: 30px;
}

.team-member-info .section-title{
	margin-bottom: 0;
}

.team-member-info .section-title p{
	text-transform: capitalize;
	margin-top: 10px;
}

.member-social-list ul{
	list-style: none;
	margin: 0;
	padding: 0;
	text-align: right;
}

.member-social-list ul li{
	display: inline-block;
	margin-right: 10px;
}

.member-social-list ul li:last-child{
	margin-right: 0;
}

.member-social-list ul li a{
	width: 36px;
	height: 36px;
	color: var(--white-color);
	background: var(--accent-color);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.4s ease-in-out;
}

.member-social-list ul li a:hover{
	background: var(--primary-color);
}

.member-social-list ul li a i{
	color: inherit;
	font-size: 18px;
}

.member-content-body{
	background-color: var(--secondary-color);
	border-radius: 20px;
	margin-bottom: 40px;
	padding: 30px;
}

.member-content-body ul{
    margin: 0;
    padding: 0;
    list-style: none;
}

.member-content-body ul li{
	font-family: var(--accent-font);
	font-size: 20px;
    font-weight: 600;
	line-height: 1.5em;
    text-transform: capitalize;
    color: var(--primary-color);
    display: flex;
	justify-content: space-between;
    margin-bottom: 15px;
}

.member-content-body ul li:last-child{
    margin-bottom: 0;
}

.member-content-body ul li span{
	font-family: var(--default-font);
    font-size: 16px;
    font-weight: 400;
    text-transform: none;
    color: var(--text-color);
	display: inline-block;
    width: 80%;
}

.member-about-nav{
	margin-bottom: 30px;
}

.member-about-nav ul{
	list-style: none;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
	gap: 10px 0;
    padding: 0;
    margin: 0;
	border-bottom: 2px solid var(--divider-color);
}

.member-about-nav ul li{
    display: inline-block;
	width: 33.33%;
}

.member-about-nav ul li .nav-link{
	position: relative;
	width: 100%;
    background: transparent;
	border: none;
    color: var(--text-color);
	font-family: var(--accent-font);
	font-size: 16px;
    font-weight: 700;
    line-height: 1.2em;
	padding: 0 5px 20px 5px;
    text-transform: capitalize;
    transition: all 0.3s ease-in-out;
}

.member-about-nav ul li .nav-link.active,
.member-about-nav ul li .nav-link:hover{
    background: transparent;
    color: var(--accent-color);
}

.member-about-nav ul li .nav-link::before{
    content: '';
    display: block;
    position: absolute;
    bottom: 0;
    left: auto;
    right: 0;
    background: var(--accent-color);
    width: 0;
    height: 3px;
    transition: all 0.4s ease-in-out;
}

.member-about-nav ul li .nav-link.active:before,
.member-about-nav ul li .nav-link:hover:before{
	width: 100%;
    left: 0;
    right: auto;
}

.member-about-content .section-title{
	margin-bottom: 0;
}

.skills-progress-bar{
	margin-bottom: 30px;
}

.skills-progress-bar:last-child{
	margin-bottom: 0;
}

.skillbar .skill-data{
	display: flex;
	justify-content: space-between;
	margin-bottom: 15px;
}

.skill-data .skill-title{
	font-size: 16px;
	text-transform: capitalize;
}

.skill-data .skill-no{
	color: var(--primary-color);
	font-size: 16px;
	margin-left: 25px;
}

.skill-progress{
	position: relative;
	background: var(--secondary-color);
	border-radius: 99px;
	width: 100%;
	height: 16px;
}

.skill-progress .count-bar{
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	background-color: var(--accent-color);
	border-radius: 99px;
}

.team-member-experience{
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	gap: 30px;
}

.team-experience-box,
.team-contact-form{
	width: calc(50% - 15px);
}

.team-experience-body ul{
	list-style: none;
	margin: 0;
	padding: 0;
	display: flex;
	flex-wrap: wrap;
	gap: 30px;
}

.team-experience-body ul li{
	background: url('../images/icon/icon-check-dark.svg') no-repeat;
    background-position: left center;
    background-size: 26px auto;
	width: calc(50% - 15px);
	color: var(--primary-color);
	text-transform: capitalize;
	line-height: 1.5em;
	padding-left: 30px;
}

.team-contact-form{
	background-color: var(--secondary-color);
	border-radius: 30px;
	padding: 40px;
}

/************************************/
/***   28. Testimonial Page css   ***/
/************************************/

.page-testimonials{
	padding: 100px 0 70px;
}

.page-testimonials .testimonial-item{
    margin-bottom: 30px;
    height: calc(100% - 30px);
}

/************************************/
/***    29. Image Gallery css     ***/
/************************************/

.page-gallery{
	padding: 100px 0 70px;
}

.page-gallery-box .photo-gallery{
	height: calc(100% - 30px);
	margin-bottom: 30px;
}

.page-gallery-box .photo-gallery a{
	cursor: none;
}

.page-gallery-box .photo-gallery figure{
	display: block;
	border-radius: 20px;
}

.page-gallery-box .photo-gallery img{
    width: 100%;
    aspect-ratio: 1 / 0.81;
	object-fit: cover;
	border-radius: 20px;
}

/************************************/
/***     30. Video Gallery css    ***/
/************************************/

.page-video-gallery{
	padding: 100px 0 70px;
}

.video-gallery-image{
	overflow: hidden;
	height: calc(100% - 30px);
	margin-bottom: 30px;
}

.video-gallery-image a{
	position: relative;
	display: block;
	cursor: none;
}

.video-gallery-image a::before{
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--primary-color);
	border-radius: 20px;
    opacity: 0%;
    visibility: hidden;
    width: 100%;
    height: 100%;
    z-index: 1;
    transform: scale(0);
    transition: all 0.4s ease-in-out;
}

.video-gallery-image:hover a::before{
    opacity: 50%;
    visibility: visible;
    transform: scale(1);
}

.video-gallery-image a::after{
    content: '\f04b';
	font-family: 'FontAwesome';
    position: absolute;
    top: 50%;
    left: 50%;
    right: 0;
    transform: translate(-50%, -50%);
	font-size: 20px;
	background: var(--accent-color);
	color: var(--white-color);
    border-radius: 50%;
    height: 60px;
    width: 60px;
    cursor: none;
	display: flex;
	align-items: center;
	justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.5s ease-in-out;
    z-index: 1;
}

.video-gallery-image:hover a::after{
    opacity: 1;
    visibility: visible;
}

.video-gallery-image img{
	width: 100%;
    aspect-ratio: 1 / 0.81;
	object-fit: cover;
	border-radius: 20px;
}

/************************************/
/***       31. FAQs Page css      ***/
/************************************/

.page-faqs{
	padding: 100px 0;
}

.page-faqs-accordion{
	margin-bottom: 60px;
}

.page-faqs-accordion:last-child{
	margin-bottom: 0;
}

/************************************/
/***    32. Contact Us Page css   ***/
/************************************/

.page-contact-us{
	padding: 100px 0;
}

.contact-info-box{
	background-color: var(--white-color);
	border: 1px solid var(--divider-color);
	border-radius: 30px;
	padding: 80px;
	box-shadow: 0px -10px 40px 5px #00000005;
	display: flex;
	flex-wrap: wrap;
	gap: 30px 100px;
}

.contact-info-item{
	width: calc(33.33% - 66.66px);
	position: relative;
	display: flex;
	align-items: start;
}

.contact-info-item::before{
	content: '';
	position: absolute;
	top: 0;
	bottom: 0;
	right: -50px;
	height: 100%;
	width: 1px;
	background-color: var(--divider-color);
}

.contact-info-item:last-child:before{
	display: none;
}

.contact-info-item .icon-box{
	position: relative;
	margin-right: 20px;
	padding-bottom: 5px;
}

.contact-info-item .icon-box:before{
	content: '';
	position: absolute;
	left: 50%;
	bottom: 0px;
	transform: translateX(-50%);
	background-color: var(--accent-color);
	border-radius: 50%;
	width: 30px;
	height: 30px;
	z-index: 0;
}

.contact-info-item .icon-box img{
	position: relative;
	width: 100%;
	max-width: 50px;
	z-index: 1;
}

.contact-info-content{
	width: calc(100% - 70px);
}

.contact-info-content h3{
	font-size: 20px;
	font-weight: 600;
	text-transform: capitalize;
	margin-bottom: 10px;
}

.contact-info-content p{
	margin: 0;
}

.contact-info-content p a{
	color: inherit;
	transition: all 0.3s ease-in-out;
}

.contact-info-content p a:hover{
	color: var(--primary-color);
}

.contact-form-section .container-fluid{
	padding: 0;
}

.google-map-iframe,
.google-map-iframe iframe{
    width: 100%;
    height: 100%;
}

.contact-form-box{
	background: var(--secondary-color);
	padding: 6.25vw;
}

.contact-form .form-control{
	font-size: 16px;
	font-weight: 400;
	line-height: 1.5em;
	color: var(--text-color);
	background: var(--white-color);
	border: 1px solid var(--divider-color);
	border-radius: 10px;
	padding: 17px 20px;
	box-shadow: none;
	outline: none;
}

.contact-form .form-control::placeholder{
	color: var(--text-color);
}

/************************************/
/***     33. Donation Page css    ***/
/************************************/

.page-donation{
	padding: 100px 0;
}

.donation-box{
	background: var(--white-color);
	border: 1px solid var(--divider-color);
	box-shadow: 0px -10px 40px 5px #00000008;
	border-radius: 30px;
	padding: 120px;
}

.campaign-donate-form .form-control{
	color:  var(--text-color);
	font-weight: 400;
}

.campaign-donate-form .form-control::placeholder{
	color: var(--text-color);
	font-weight: 400;
}

.campaign-donate-value .donate-value-box{
	gap: 15px;
}

.campaign-donate-form .donate-value{
	width: calc(16.66% - 12.5px);
}

.donate-payment-method{
	margin-bottom: 40px;
}

.donate-payment-method .section-title,
.donar-personal-info .section-title{
	border-bottom: 1px solid var(--divider-color);
	margin-bottom: 40px;
	padding-bottom: 40px;
}

.donate-payment-type{
	display: flex;
	flex-wrap: wrap;
	gap: 20px 30px;
}

.donate-payment-type .payment-method{
	display: flex;
	align-items: center;
}

.donate-payment-type .payment-method input{
	height: 24px;
	width: 24px;
}

.payment-method label{
	text-transform: capitalize;
	padding-left: 10px;
}

/************************************/
/***    34. 404 Error Page css    ***/
/************************************/

.error-page{
	padding: 100px 0;
}

.error-page-image{
	text-align: center;
	margin-bottom: 30px;
}

.error-page-image img{
	width: 100%;
	max-width: 50%;
}

 .error-page-content{
	text-align: center;
}

.error-page-content .section-title{
	margin-bottom: 15px;
}

/************************************/
/***      35. Privacy Policy Page css    ***/
/************************************/

.page-privacy-policy{
	padding: 100px 0;
}

/************************************/
/***      36. Terms & Conditions Page css      ***/
/************************************/

.page-terms-conditions{
	padding: 100px 0;
}

/************************************/
/***      37. Responsive css      ***/
/************************************/

@media only screen and (max-width: 991px){

	.btn-default{
		padding: 16px 50px 16px 20px;
	}
	
	.btn-default::before{
		transform: translate(-20px, -50%);
	}
	
	.btn-default:hover::before{
		transform: translate(-17px, -50%);
	}

	.readmore-btn{
		padding-right: 35px;
	}
	
	.readmore-btn::before{
		width: 26px;
		height: 26px;
	}

	.slicknav_nav li,
	.slicknav_nav ul{
        display: block;
    }

	.responsive-menu,
    .navbar-toggle{
        display: block;
    }

	.contact-now-box{
		display: none;
	}

	.section-row{
		margin-bottom: 40px;
	}

	.section-title{
		margin-bottom: 30px;
	}

	.section-title h1{
		font-size: 46px;
	}

	.section-title h2{
		font-size: 38px;
	}

	.section-title p{
		margin-top: 10px;
	}

	.section-title-content{
		margin-top: 10px;
		margin-left: 0;
	}

	.hero-content{
		margin-right: 0px;
	}

	.hero{
		padding: 150px 0 60px;
	}

	.hero.hero-slider-layout .hero-slide{
		padding: 150px 0 80px;
	}
	
	.hero.hero-slider-layout .hero-pagination{
		bottom: 30px;
		padding-left: 15px;
	}

	.hero-content .section-title,
	.hero-body{
		margin-bottom: 40px;
	}

	.hero-list ul li{
		background-size: 22px auto;
		margin-bottom: 15px;
		padding-left: 30px;
	}

	.hero-help-families{
		max-width: 100%;
	}

	.about-us{
		padding: 50px 0;
	}

	.about-us-images{
		max-width: 76%;
		margin: 0 auto;
		margin-bottom: 30px;
	}

	.about-img-2{
		border-width: 10px;
	}

	.need-fund-box{
		max-width: 135px;
		padding: 15px;
		bottom: 10px;
	}

	.need-fund-box img{
		max-width: 50px;
	}

	.need-fund-box p{
		font-size: 14px;
	}

	.about-support-box .icon-box img{
		max-width: 45px;
	}

	.helped-fund-img{
		margin-bottom: 15px;
	}

	.helped-fund-img figure{
		max-width: 100px;
	}

	.helped-fund-content h2{
		font-size: 24px;
	}

	.our-services{
		padding: 50px 0;
	}

	.service-item{
		border-radius: 20px;
		padding: 30px;
	}

	.service-content,
	.service-image{
		margin-bottom: 20px;
	}
	
	.service-content h3{
		margin-bottom: 10px;
	}

	.service-image figure{
		max-width: 160px;
	}

	.section-footer-text{
		margin-top: 20px;
	}

	.what-we-do{
		padding: 50px 0;
	}

	.what-we-do-content{
		margin-bottom: 30px;
	}

	.what-we-item{
		margin-bottom: 25px;
		padding-bottom: 25px;
	}

	.what-we-item .icon-box{
		margin-right: 20px;
	}

	.what-we-item .icon-box:before{
		width: 32px;
		height: 32px;
	}

	.what-we-item .icon-box img{
		max-width: 60px;
	}

	.what-we-item-content{
		width: calc(100% - 80px);
	}

	.what-we-do-images{
		padding-left: 110px;
	}

	.what-we-do-img-1 img{
        aspect-ratio: 1 / 1.05;
    }

	.what-we-do-img-2{
		max-width: 220px;
	}
	
	.donate-now-box{
		left: 30px;
	}
	
	.our-causes{
		padding: 50px 0 20px;
	}

	.causes-item{
		border-radius: 20px;
	}

	.causes-image,
	.causes-content{
		margin-bottom: 20px;
	}

	.causes-image figure{
		border-radius: 20px;
	}

	.causes-image img{
		aspect-ratio: 1 / 0.75;
		border-radius: 20px;
	}

	.causes-button .btn-default{
		padding: 16px;
	}

	.why-choose-us{
		padding: 50px 0;
	}

	.why-choose-images{
		width: 100%;
		max-width: 75%;
		margin: 0 auto 30px;
	}

	.why-choose-image-2{
		max-width: 220px;
	}

	.why-choose-content{
		margin-left: 0px;
	}

	.why-choose-list ul{
		gap: 20px;
	}

	.why-choose-list ul li{
		width: calc(50% - 10px);
		background-size: 22px auto;
		padding-left: 30px;
	}

	.why-choose-counters{
		gap: 30px 40px;
		margin-top: 30px;
		padding-top: 30px;
	}

	.why-choose-counter-item{
		width: calc(33.33% - 26.67px);
	}

	.why-choose-counter-item::before{
		right: -20px;
	}

	.why-choose-counter-item h2{
		font-size: 38px;
	}

	.our-program{
		padding: 50px 0;
	}

	.program-item{
		border-radius: 20px;
		padding: 30px;
	}

	.program-image,
	.program-content{
		margin-bottom: 20px;
	}

	.our-program .section-footer-text{
		margin-top: 20px;
	}

	.scrolling-ticker-box{
		--gap: 30px;
	}

	.scrolling-ticker-box .scrolling-content span img{
		margin-right: 30px;
	}
	
	.scrolling-ticker-box .scrolling-content span{
		font-size: 70px;
	}

	.our-features{
		padding: 50px 0;
	}

	.our-features-item{
		width: 100%;
		align-items: center;
		gap: 20px 30px;
	}

	.our-features-item:nth-child(even){
		flex-direction: row-reverse;
	}

	.our-features-image,
	.our-features-content{
		width: calc(50% - 15px);
	}

	.our-features-image figure{
		border-radius: 20px;
	}

	.our-features-image img{
		aspect-ratio: 1 / 0.72;
		border-radius: 20px;
	}

	.our-features-body{
		width: calc(100% - 75px);
	}

	.our-features-body h2{
		font-size: 38px;
		margin-bottom: 15px;
	}

	.our-features-content .icon-box:before{
		width: 33px;
		height: 33px;
	}

	.our-features-content .icon-box img{
		max-width: 60px;
	}

	.donate-now{
		padding: 80px 0;
	}

	.intro-video-box{
		margin: 0 0 50px 0;
	}

	.donar-company-slider{
		margin-top: 80px;
	}

	.donar-company-logo img{
		max-width: 180px;
	}

	.donate-box{
		border-radius: 20px;
		padding: 30px;
	}

	.donate-value-box .form-control{
		padding: 14px 25px;
	}

	.donate-value-box .donate-value label{
		padding: 16px;
	}

	.donate-box .donate-form .form-group-btn .btn-default{
		padding: 16px;
	}

	.how-it-work{
		padding: 50px 0;
	}

	.how-it-work-item{
		width: 100%;
	}

	.how-it-work-image,
	.how-it-work-content{
		width: calc(50% - 15px);
	}

	.how-it-work-item:nth-child(even){
		flex-direction: row-reverse;
	}

	.how-it-work-image figure{
		border-radius: 20px;
	}

	.how-it-work-image img{
		aspect-ratio: 1 / 0.625;
		border-radius: 20px;
	}

	.how-it-work-content{
		border-radius: 20px;
	}

	.how-it-work-content .icon-box{
		margin-bottom: 20px;
	}

	.how-it-work-content .icon-box img{
		max-width: 50px;
	}

	.how-it-work-content .icon-box:before{
        width: 30px;
        height: 30px;
    }

	.section-footer-text.how-work-footer-text{
		margin-top: 40px;
	}

	.our-testimonials{
		background-position: bottom center;
		padding: 50px 0;
	}

	.testimonials-image{
		max-width: 80%;
		margin: 0 auto;
		padding-right: 70px;
		margin-bottom: 30px;
	}

	.testimonials-img figure,
	.testimonials-img img{
		border-radius: 20px;
	}

	.testimonials-img img{
		aspect-ratio: 1 / 1.1;
	}

	.helthcare-support-circle img{
		max-width: 140px;
	}

	.client-review-box{
		padding: 15px;
	}

	.client-review-box h2{
		font-size: 38px;
	}

	.client-review-box p{
		font-size: 14px;
	}

	.testimonials-content{
		margin-left: 0px;
	}

	.testimonial-item{
		border-radius: 20px;
		padding: 30px;
	}

	.testimonial-header{
		margin-bottom: 20px;
		padding-bottom: 20px;
	}

	.author-image{
		margin-right: 10px;
	}

	.author-content{
		width: calc(100% - 70px);
	}

	.testimonial-slider .testimonial-pagination{
		margin-top: 30px;
	}

	.our-gallery{
		padding: 50px 0 25px;
	}

	.our-gallery-nav{
		margin-bottom: 40px;
	}

	.our-gallery-nav ul{
		gap: 10px 50px;
	}

	.our-gallery-nav ul li a:before{
		right: -29px;
	}

	.gallery-item-box img{
		width: 100%;
		aspect-ratio: 1 / 0.88;
		object-fit: cover;
	}

	.our-blog{
		padding: 25px 0 20px;
	}

	.post-item{
		padding: 30px;
		border-radius: 20px;
	}

	.post-item-header,
	.post-featured-image{
		margin-bottom: 20px;
	}

	.post-item-meta{
		margin-bottom: 10px;
	}

	.main-footer{
		text-align: center;
		padding: 50px 0 0;
	}

	.footer-about,
	.footer-links-box{
		width: 100%;
	}

	.footer-about::before{
		display: none;
	}

	.footer-logo{
		margin-bottom: 30px;
	}

	.footer-contact-item p,
	.footer-social-links h3{
		margin-bottom: 10px;
	}

	.footer-social-links{
		margin-top: 30px;
	}

	.footer-links-box{
		gap: 30px;
	}

	.newsletter-form .form-group .form-control{
		width: calc(100% - 46px);
		padding: 10px 20px;
	}

	.newsletter-form .form-group .newsletter-btn{
		width: 46px;
		height: 46px;
	}

	.newsletter-form .form-group .newsletter-btn i{
		font-size: 20px;
	}

	.footer-links{
		width: calc(30% - 20px);
	}

	.footer-links.footer-service-links{
		width: calc(40% - 20px);
	}

	.footer-links h3{
		margin-bottom: 20px;
	}

	.footer-links ul li{
		margin-bottom: 10px;
	}

	.footer-copyright{
		padding: 15px 0;
		margin-top: 40px;
	}

	.page-header{
		padding: 170px 0 80px;
	}

	.page-header-box h1{
		font-size: 46px;
	}

	.our-approach{
		padding: 50px 0;
	}

	.our-approach-box-content{
		padding-bottom: 0;
	}

	.our-approach-content{
		padding-bottom: 0;
	}

	.our-approach-content,
	.our-approach-image{
		width: 100%;
	}

	.mission-vision-box{
		position: initial;
		gap: 30px;
		border-radius: 20px;
		padding: 30px;
		margin-top: 30px;
	}

	.mission-vision-item{
		width: calc(33.33% - 20px);
	}

	.mission-vision-item:before{
		right: -15px;
	}

	.mission-vision-item .icon-box img{
		max-width: 45px;
	}

	.our-approach-image figure{
		border-radius: 20px;
	}

	.our-approach-image img{
		aspect-ratio: 1 / 0.7;
		border-radius: 20px;
	}

	.how-we-help{
		padding: 50px 0;
	}

	.how-we-help:before{
		border-radius: 0 0px 20px 20px;
		height: 60%;
		width: 100%;
	}

	.how-help-item{
		border-radius: 20px;
	}

	.how-we-help-content{
		margin-bottom: 30px;
	}

	.how-we-help-body{
		margin-bottom: 30px;
	}

	.how-we-help-body ul li{
		background-size: 22px auto;
		margin-bottom: 15px;
		padding-left: 25px;
	}

	.how-help-item .icon-box{
		margin-bottom: 20px;
	}

	.how-help-item .icon-box:before{
		width: 30px;
		height: 30px;
	}

	.how-help-item .icon-box img{
		max-width: 50px;
	}

	.our-fact{
		padding: 50px 0;
	}

	.our-fact-image{
		margin-bottom: 30px;
	}

	.our-fact-image figure{
		border-radius: 20px;
	}

	.our-fact-image img{
		aspect-ratio: 1 / 0.7;
		border-radius: 20px;
	}

	.fact-counter-item{
		padding: 25px 20px;
	}

	.fact-counter-item h2{
		font-size: 46px;
		width: calc(40% - 5px);
	}

	.fact-counter-item p{
		width: calc(60% - 5px);
	}

	.fact-body-image figure{
		border-radius: 20px;
	}

	.fact-body-image img{
		aspect-ratio: 1 / 0.6;
		border-radius: 20px;
	}

	.our-team{
		padding: 50px 0 20px;
	}

	.team-item{
		border-radius: 20px;
	}

	.team-image{
		margin-bottom: 15px;
	}

	.team-content{
		margin-bottom: 10px;
	}

	.our-faqs{
		padding: 50px 0;
	}

	.faqs-content{
		margin-bottom: 30px;
	}

	.faq-accordion .accordion-header .accordion-button{
		padding: 14px 35px 14px 14px;
	}
	
	.faq-accordion .accordion-item .accordion-button::after,
	.faq-accordion .accordion-item .accordion-button.collapsed::after{
		font-size: 16px;
		right: 14px;
	}
	
	.faq-accordion .accordion-item .accordion-body{
		padding: 14px 35px 14px 14px;
	}

	.faqs-image{
		max-width: 75%;
		margin: 0 auto;
		padding-right: 100px;
	}

	.faqs-image:before{
		right: 40px;
	}

	.faqs-img-1,
	.faqs-img-2{
		border-radius: 20px;
	}

	.faqs-img-2{
		max-width: 252px;
		top: 100px;
	}

	.faqs-img-1 img{
		aspect-ratio: 1 / 1.414;
	}

	.page-services{
		padding: 50px 0 20px;
	}

	.our-features.services-our-features{
		padding: 50px 0;
	}

	.page-service-single{
		padding: 50px 0;
	}

	.page-single-sidebar{
		position: initial;
		margin: 0 0 30px 0;
	}

	.page-sidebar-catagery-list{
		border-radius: 20px;
		margin-bottom: 30px;
	}

	.page-sidebar-catagery-list h3{
		padding: 15px 20px;
	}

	.page-sidebar-catagery-list ul{
		padding: 20px;
	}

	.sidebar-cta-box{
		border-radius: 20px;
		padding: 30px;
	}

	.sidebar-cta-box .icon-box,
	.sidebar-cta-content{
		margin-bottom: 30px;
	}

	.sidebar-cta-box .icon-box img{
		max-width: 70px;
	}

	.service-feature-image,
	.service-entry{
		margin-bottom: 30px;
	}

	.service-feature-image figure{
		border-radius: 20px;
	}

	.service-feature-image img{
		aspect-ratio: 1 / 0.5;
		border-radius: 20px;
	}

	.bringing-quality-box,
	.service-entry-content-list,
	.service-entry-steps{
		margin-top: 30px;
	}

	.service-entry p{
		margin-bottom: 15px;
	}
	
	.service-entry h2{
		font-size: 38px;
		margin-bottom: 15px;
	}

	.service-entry ul{
		gap: 20px;
		border-radius: 20px;
		padding: 30px;
		margin: 30px 0;
	}

	.service-entry ul li{
		width: calc(33.33% - 13.33px);
		background-size: 22px auto;
	}

	.service-entry-image figure,
	.service-entry-image img{
		border-radius: 20px;
	}

	.service-entry-content-box .icon-box img{
		max-width: 40px;
	}

	.service-entry-content-box .icon-box:before{
		width: 24px;
		height: 24px;
	}

	.service-entry-step-list{
		margin-top: 30px;
	}

	.service-entry-step-item{
		border-radius: 20px;
		padding: 20px;
	}

	.service-entry-step-box{
		width: calc(100% - 60px);
	}

	.service-entry-step-no{
		margin-right: 20px;
	}

	.service-entry-step-no h2{
		margin-bottom: 0;
	}

	.service-entry-step-item .icon-box img{
		max-width: 45px;
	}

	.service-entry-step-item .icon-box:before{
		width: 24px;
		height: 24px;
	}

	.page-blog{
		padding: 50px 0;
	}

	.page-pagination{
		margin-top: 10px;
	}

	.page-single-post{
        padding: 50px 0;
    }
    
    .post-image{
        margin-bottom: 20px;
    }

	.post-image figure,
	.post-image img{
		border-radius: 20px;
	}
    
    .post-entry h1,
    .post-entry h2,
    .post-entry h3,
    .post-entry h4,
    .post-entry h5,
    .post-entry h6{
        margin: 0 0 0.417em;
    }
    
    .post-entry h2{
        font-size: 38px;
    }
    
    .post-entry p{
        margin-bottom: 15px;
    }

	.post-entry ul li{
		font-size: 16px;
		margin-bottom: 10px;
	}
    
    .post-entry blockquote{
        background-position: 25px 25px;
        background-size: 50px;
        padding: 25px 25px 25px 85px;
		border-radius: 20px;
        margin-bottom: 20px;
    }
    
    .post-entry blockquote p{
        font-size: 18px;
    }
    
    .post-tags{
        margin-bottom: 20px;
    }

    .tag-links{
        font-size: 20px;
    }

    .post-tags .tag-links a{
		padding: 12px 15px;
    }
    
    .post-social-sharing ul{
        text-align: left;
    }

	.page-programs{
		padding: 50px 0 20px;
	}

	.page-program-single{
		padding: 50px 0;
	}

	.program-feature-image,
	.program-entry{
		margin-bottom: 30px;
	}

	.program-feature-image figure{
		border-radius: 20px;
	}

	.program-feature-image img{
		aspect-ratio: 1 / 0.5;
		border-radius: 20px;
	}

	.building-stability-box,
	.program-why-choose{
		margin-top: 30px;
	}

	.program-entry p{
		margin-bottom: 15px;
	}
	
	.program-entry h2{
		font-size: 38px;
		margin-bottom: 15px;
	}

	.program-entry ul{
		gap: 15px 20px;
		border-radius: 20px;
		margin-top: 30px;
	}

	.program-entry ul li{
		width: calc(50% - 10px);
		background-size: 22px auto;
	}

	.program-entry-video{
		margin: 30px 0;
	}

	.program-entry-video-image figure,
	.program-entry-video-image img{
		border-radius: 20px;
	}

	.program-entry-video-image img{
		aspect-ratio: 1 / 0.74;
	}

	.program-why-choose-item .icon-box{
		margin-bottom: 20px;
	}

	.program-why-choose-item .icon-box:before{
		width: 28px;
		height: 28px;
	}

	.program-why-choose-item .icon-box img{
		max-width: 45px;
	}

	.section-footer-text.program-why-choose-footer{
		margin-top: 30px;
	}

	.page-team{
		padding: 50px 0 20px;
	}

	.page-team-single{
		padding: 50px 0;
	}

	.page-team-single-box{
		gap: 50px 30px;
	}

	.team-member-image,
	.team-member-content{
		width: 100%;
	}

	.team-member-image img{
		aspect-ratio: 1 / 0.8;
	}

	.member-content-body{
		padding: 20px;
		margin-bottom: 30px;
	}

	.member-content-body ul li{
		margin-bottom: 10px;
	}

	.member-about-nav ul li .nav-link{
		padding: 0 5px 15px 5px;
	}

	.skills-progress-bar{
		margin-bottom: 20px;
	}

	.skillbar .skill-data{
		margin-bottom: 10px;
	}

	.team-experience-box,
	.team-contact-form{
		width: 100%;
	}

	.team-experience-body ul{
		gap: 15px 20px;
	}

	.team-experience-body ul li{
		width: calc(50% - 10px);
		background-size: 22px auto;
	}

	.team-contact-form{
		padding: 30px;
		border-radius: 20px;
	}

	.page-testimonials{
		padding: 50px 0 20px;
	}

	.page-gallery{
		padding: 50px 0 20px;
	}

	.page-video-gallery{
		padding: 50px 0 20px;
	}

	.page-faqs{
		padding: 50px 0;
	}

	.page-faqs-accordion{
		margin-bottom: 40px;
	}

	.page-contact-us{
		padding: 50px 0;
	}

	.contact-info-box{
		padding: 40px;
	}

	.contact-info-item{
		width: calc(50% - 50px);
	}

	.contact-info-item:nth-child(2n + 2)::before{
		display: none;
	}

	.contact-info-item .icon-box{
		margin-right: 15px;
	}

	.contact-info-item .icon-box img{
		max-width: 45px;
	}

	.contact-info-content{
		width: calc(100% - 60px);
	}

	.google-map-iframe,
	.google-map-iframe iframe{
		height: 450px;
	}

	.contact-form-box{
		padding: 50px;
	}

	.contact-form .form-control{
		padding: 12px 15px;
	}

	.page-donation{
		padding: 50px 0;
	}

	.donation-box{
		padding: 60px 30px;
		border-radius: 20px;
	}

	.campaign-donate-form .form-control{
		padding: 12px 20px;
	}

	.campaign-donate-form .donate-value label{
		padding: 12px;
	}

	.donate-payment-method{
		margin-bottom: 30px;
	}

	.donate-payment-method .section-title,
	.donar-personal-info .section-title{
		margin-bottom: 30px;
		padding-bottom: 30px;
	}

	.donate-payment-type .payment-method input{
		height: 20px;
		width: 20px;
	}

	.error-page{
		padding: 50px 0;
	}
	
	.error-page-image{
		margin-bottom: 20px;
	}

	.error-page-image img{
		max-width: 80%;
	}
}

@media only screen and (max-width: 767px){

	.section-row{
		margin-bottom: 30px;
	}

	.section-title h3{
		font-size: 14px;
		padding-left: 25px;
		margin-bottom: 10px;
	}

	.section-title h3::before{
		width: 16px;
		height: 16px;
	}

	.section-title h1{
		font-size: 28px;
	}

	.section-title h2{
		font-size: 26px;
	}	

	.video-play-button a{
		height: 45px;
		width: 45px;
	}

	.video-play-button a i{
		font-size: 20px;
	}

	.hero::before{
		background: linear-gradient(270deg, rgba(2, 13, 25, 0.3) 16.33%, rgba(2, 13, 25, 0.8) 100%), linear-gradient(360deg, rgba(2, 13, 25, 0) 87.52%, #020D19 101.14%);
	}

	.hero-body{
		gap: 20px;
		justify-content: space-between;
	}

	.hero-list,
	.hero-help-families{
		width: 100%;
	}

	.hero-list:before{
		top: auto;
		right: 0;
		bottom: -15px;
		left: 0;
		height: 1px;
		width: 100%;
	}

	.hero-help-families h3{
		font-size: 18px;
		margin-bottom: 5px;
	}

	.about-us-images{
		max-width: 100%;
		padding: 0 0 125px 70px;
	}

	.about-img-2{
		border-width: 5px;
		max-width: 210px;
	}

	.need-fund-box{
        max-width: 125px;
        padding: 10px;
        bottom: 5px;
    }

	.need-fund-box img{
        max-width: 35px;
		margin-bottom: 0;
    }

	.about-us-body-content,
	.helped-fund-item{
		width: 100%;
	}

	.about-support-box{
		margin-bottom: 20px;
		padding-bottom: 20px;
	}

	.helped-fund-item{
		padding: 0 20px 20px;
		max-width: 68%;
	}

	.helped-fund-img figure{
        max-width: 80px;
    }

	.helped-fund-content h2{
        font-size: 22px;
    }

	.helped-fund-content h3{
		font-size: 18px;
	}

	.service-item{
        padding: 25px 20px;
    }

	.service-content h3{
		font-size: 18px;
		margin-bottom: 5px;
	}

	.service-image figure{
		max-width: 120px;
	}

	.section-footer-text{
        margin-top: 10px;
    }

	.section-footer-text p span{
		padding: 3px 8px;
	}

	.what-we-item{
        margin-bottom: 20px;
        padding-bottom: 20px;
    }

	.what-we-item .icon-box{
        margin-right: 10px;
    }

	.what-we-item .icon-box img{
        max-width: 50px;
    }

	.what-we-item-content{
        width: calc(100% - 60px);
    }

	.what-we-item-content h3{
		font-size: 18px;
		margin-bottom: 5px;
	}

	.what-we-do-images{
        padding-left: 70px;
    }

    .what-we-do-img-1 figure{
		border-radius: 20px;
	}

    .what-we-do-img-1 img{
        aspect-ratio: 1 / 1.5;
		border-radius: 20px;
    }
	
	.what-we-do-img-2{
		max-width: 140px;
		bottom: 30px;
    }

	.donate-now-box{
		left: 15px;
        top: 15px;
    }
	
	.donate-now-box a{
		font-size: 16px;
		gap: 10px;
		padding: 20px 10px;
	}

	.donate-now-box a img{
		max-width: 20px;
	}

	.causes-content h3{
		font-size: 18px;
		margin-bottom: 5px;
	}	

	.why-choose-images{
		max-width: 100%;
		padding: 0 40px 30px 0;
	}

	.why-choose-image-2{
        max-width: 160px;
    }

	.why-choose-list ul{
        gap: 15px;
    }

	.why-choose-list ul li{
		width: 100%;
	}

	.why-choose-counters{
        gap: 20px;
        margin-top: 20px;
        padding-top: 20px;
    }

	.why-choose-counter-item{
        width: calc(33.33% - 13.33px);
    }

	.why-choose-counter-item::before{
        right: -10px;
    }

	.why-choose-counter-item h2{
        font-size: 26px;
    }

	.why-choose-counter-item p{
		font-size: 14px;
	}

	.program-item{
        padding: 36px;
		  text-align: center;
    }

	.program-content h3{
		font-size: 18px;
	}

	.scrolling-ticker-box{
		--gap: 20px;
	}

	.scrolling-ticker-box .scrolling-content span img{
		width: 35px;
		margin-right: 20px;
	}
	
	.scrolling-ticker-box .scrolling-content span{
		font-size: 40px;
	}

	.our-features-item:nth-child(even){
		flex-direction: column;
	}

	.our-features-image,
	.our-features-content{
        width: 100%;
    }

	.our-features-image img{
        aspect-ratio: 1 / 0.67;
    }

	.our-features-body{
		width: calc(100% - 60px);
	}

	.our-features-body h2{
		font-size: 26px;
		margin-bottom: 10px;
	}

	.our-features-body h3{
		font-size: 18px;
		margin-bottom: 5px;
	}

	.our-features-content .icon-box img{
		max-width: 45px;
	}

	.our-features-content .icon-box:before{
		width: 25px;
		height: 25px;
	}

	.donate-now{
        padding: 50px 0;
    }

	.intro-video-box{
        margin: 0 0 40px 0;
    }

	.intro-video-box .video-play-button a{
		width: 60px;
		height: 60px;
	}

	.intro-video-box .video-play-button a:before,
	.intro-video-box .video-play-button a::after{
		top: -33%;
		left: -33%;
	}

	.intro-video-box .video-play-button a i{
		font-size: 25px;
	}

	.donar-company-slider{
        margin-top: 40px;
    }

	.donar-company-logo img{
        max-width: 140px;
    }

	.donate-box{
        padding: 20px;
    }

	.donate-form .form-control{
        padding: 10px 25px;
    }

	.donate-form .donate-value label{
		font-size: 14px;
        padding: 10px;
    }
	
	.how-it-work-item{
		gap: 20px;
	}

	.how-it-work-image,
	.how-it-work-content{
        width: 100%;
    }

	.how-it-work-content .icon-box img{
        max-width: 45px;
    }

	.how-it-work-body h3{
		font-size: 18px;
		margin-bottom: 5px;
	}

	.testimonials-image{
		max-width: 100%;
		padding-right: 50px;
	}

	.helthcare-support-circle{
		top: 30px;
	}

	.helthcare-support-circle img{
        max-width: 100px;
    }

	.client-review-box{
        padding: 12px;
		bottom: 20px;
    }

	.client-review-box h2{
        font-size: 26px;
    }

	.testimonial-item{
		padding: 20px;
	}

	.author-info,
	.testimonial-rating{
		width: 100%;
	}

	.author-content h3{
		font-size: 18px;
	}

	.testimonial-rating{
		text-align: left;
	}

	.testimonial-content p{
		font-size: 14px;
	}

	.our-gallery-nav{
        margin-bottom: 30px;
    }

	.our-gallery-nav ul{
        gap: 10px 40px;
    }

	.our-gallery-nav ul li a:before{
        right: -24px;
    }

	.gallery-item-boxes{
		left: 0;
	}

	.gallery-item-box{
		width: calc(50% - 0.2px);
	}

	.post-item{
		padding: 20px;
	}

	.post-item-header,
	.post-featured-image{
        margin-bottom: 15px;
    }

	.post-item-content h2{
		font-size: 18px;
	}

	.footer-logo{
        margin-bottom: 20px;
    }

	.footer-contact-item{
		width: 100%;
	}
	
	.footer-social-links h3,
	.footer-contact-item h3{
		font-size: 18px;
	}

	.footer-links,
	.footer-links.footer-service-links{
        width: 100%;
    }

	.footer-links h3{
        font-size: 18px;
		margin-bottom: 15px;
    }

	.footer-copyright{
        padding: 10px 0;
        margin-top: 30px;
    }

	.page-header-box h1{
		font-size: 28px;
	}

	.mission-vision-box{
		padding: 20px;
	}

	.mission-vision-item{
		width: 100%;
	}

	.mission-vision-item:before{
		width: 100%;
		height: 1px;
		top: auto;
		right: 0;
		bottom: -15px;
		left: 0;
	}

	.mission-vision-item:nth-child(3n + 3):before{
		display: block;
	}

	.mission-vision-item:last-child:before{
		display: none;
	}

	.mission-vision-item .icon-box{
		margin-bottom: 10px;
	}

	.mission-vision-content h3{
		font-size: 18px;
		margin-bottom: 5px;
	}

	.how-we-help:before{
        height: 44%;
    }

	.how-help-item{
		width: 100%;
	}

	.how-help-item .icon-box{
        margin-bottom: 15px;
    }

	.how-help-item .icon-box img{
        max-width: 45px;
    }

	.how-help-item-content h3{
		font-size: 18px;
		margin-bottom: 5px;
	}
	
	.our-fact-image img{
		aspect-ratio: 1 / 0.947;
	}

	.fact-counter-list,
	.fact-body-image{
		width: 100%;
	}

	.fact-counter-item h2{
        font-size: 30px;
        width: calc(30% - 5px);
    }

	.fact-counter-item p{
        width: calc(70% - 5px);
    }

	.fact-body-image figure,
	.fact-body-image img{
		height: auto;
	}

	.fact-body-image img{
        aspect-ratio: 1 / 0.98;
    }

	.team-content h3{
		font-size: 18px;
	}

	.team-content p{
		font-size: 14px;
	}

	.faq-accordion .accordion-header .accordion-button{
		font-size: 18px;
		padding: 12px 30px 12px 12px;
	}

	.faq-accordion .accordion-item .accordion-button::after,
	.faq-accordion .accordion-item .accordion-button.collapsed::after{
		right: 12px;
	}

	.faq-accordion .accordion-item .accordion-body{
		padding: 12px;
	}
	
	.faq-accordion .accordion-item .accordion-body p{
		font-size: 14px;
	}

	.faqs-image{
        max-width: 100%;
		padding-right: 75px;
    }

	.faqs-image:before{
		right: 30px;
		bottom: 15px;
		width: 20px;
		height: 100px;
	}

	.faqs-img-2{
		max-width: 170px;
		top: 60px;
		border-width: 4px;
	}

	.page-sidebar-catagery-list h3{
		font-size: 18px;
	}

	.page-sidebar-catagery-list ul li{
		padding-bottom: 15px;
		margin-bottom: 15px;
	}

	.page-sidebar-catagery-list ul li a{
		padding-right: 25px;
	}

	.page-sidebar-catagery-list ul li a::before{
		width: 16px;
		height: 16px;
	}

	.sidebar-cta-box{
        padding: 20px;
    }

	.sidebar-cta-box .icon-box,
	.sidebar-cta-content{
        margin-bottom: 20px;
    }

	.sidebar-cta-box .icon-box img{
        max-width: 60px;
    }

	.sidebar-cta-content p{
		margin-bottom: 10px;
	}

	.sidebar-cta-content h3{
		font-size: 18px;
	}

	.service-feature-image{
        margin-bottom: 20px;
    }

	.service-feature-image img{
        aspect-ratio: 1 / 0.65;
    }

	.service-entry h2{
        font-size: 26px;
    }

	.service-entry ul{
        gap: 15px;
        padding: 20px;
        margin: 20px 0;
    }

	.service-entry ul li{
        width: 100%;
    }

	.service-entry-content-item{
		gap: 20px;
	}

	.service-entry-image,
	.service-entry-content-box{
		width: 100%;
	}

	.service-entry-content h3{
		font-size: 18px;
	}

	.service-entry-step-list{
        margin-top: 20px;
    }

	.service-entry-step-item{
		border-radius: 12px;
		padding: 15px;
	}

	.service-entry-step-no{
        margin-right: 10px;
    }

	.service-entry-step-box{
        width: calc(100% - 48px);
    }

	.service-entry-step-content h3{
		font-size: 18px;
		margin-bottom: 5px;
	}

	.service-entry-step-content p{
		font-size: 14px;
	}

	.service-entry-step-item .icon-box{
		margin-left: 10px;
	}

	.service-entry-step-item .icon-box img{
        max-width: 38px;
    }

	.service-entry-step-item .icon-box:before{
        width: 20px;
        height: 20px;
    }

	.post-image img{
        aspect-ratio: 1 / 0.7;
    }
    
    .post-entry blockquote{
        background-position: 15px 12px;
        padding: 60px 15px 15px 15px;
    }
    
    .post-entry h2{
        font-size: 26px;
    }

	.program-feature-image{
        margin-bottom: 20px;
    }

	.program-feature-image img{
        aspect-ratio: 1 / 0.65;
    }

	.program-entry h2{
        font-size: 26px;
    }

	.program-entry ul{
		margin-top: 20px;
    }

	.program-entry ul li{
        width: 100%;
    }

	.program-entry-video{
        margin: 20px 0;
    }

	.program-entry-video-item{
		width: 100%;
	}

	.program-entry-video-image img{
        aspect-ratio: 1 / 0.67;
    }

	.program-why-choose-list{
		gap: 20px;
	}

	.program-why-choose-item{
		width: 100%;
	}

	.program-why-choose-item .icon-box{
        margin-bottom: 15px;
    }

	.program-why-choose-content h3{
		font-size: 18px;
		margin-bottom: 5px;
	}

	.team-member-image img{
		aspect-ratio: 1 / 1.06;
	}

	.team-member-title,
	.member-social-list{
		width: 100%;
	}

	.member-social-list ul{
		text-align: left;
	}

	.member-content-body ul li{
		font-size: 18px;
	}

	.member-content-body ul li span{
		width: 65%;
	}

	.member-about-nav ul li .nav-link{
        padding: 0 16px 16px 16px;
    }

	.team-experience-body ul li{
		width: 100%;
	}

	.contact-info-box{
        padding: 20px;
    }

	.contact-info-item{
		width: 100%;
	}

	.contact-info-item::before{
		top: auto;
		right: 0;
		bottom: -15px;
		left: 0;
		height: 1px;
		width: 100%;
	}

	.contact-info-item:nth-child(2n + 2)::before{
		display: block;
	}

	.contact-info-item .icon-box:before{
		width: 25px;
		height: 25px;
	}

	.contact-info-item .icon-box img{
        max-width: 40px;
    }

	.contact-info-content{
        width: calc(100% - 55px);
    }

	.contact-info-content h3{
		font-size: 18px;
		margin-bottom: 5px;
	}

	.google-map-iframe,
	.google-map-iframe iframe{
		height: 350px;
	}

	.contact-form-box{
        padding: 30px 20px;
    }

	.donation-box{
		padding: 30px 20px;
	}

	.campaign-donate-form .form-control{
		padding: 12px 15px;
	}
	
	.campaign-donate-form .donate-value{
		width: calc(33.33% - 10px);
	}

	.donate-payment-method .section-title,
	.donar-personal-info .section-title{
		margin-bottom: 20px;
		padding-bottom: 20px;
	}
	
	.donate-payment-type .payment-method input{
		height: 16px;
		width: 16px;
	}
}


/************************************/
/*** 	 38. Sweet Alert CSS	  ***/
/************************************/	

.swal2-title-custom {
    font-family: var(--accent-font) !important;
    color: var(--primary-color) !important;
}
.swal2-btn-custom {
    font-family: var(--accent-font) !important;
    background: var(--accent-color) !important;
    color: var(--white-color) !important;
    border-radius: 100px !important;
    font-weight: 700 !important;
    padding: 12px 36px !important;
    font-size: 1rem !important;
}
.swal2-popup-custom {
    border-radius: 18px !important;
    font-family: var(--accent-font) !important;
}
/************************************/	
/*** Floating Action Buttons ***/
/************************************/	

.upf-floating-buttons {
  position: fixed;
  right: 1.5rem;
  bottom: 1.5rem;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 1rem;
  pointer-events: none;
}
.upf-fab {
  pointer-events: auto;
  width: 56px;
  height: 56px;
  border: none;
  border-radius: 50%;
  background: var(--primary-color);
  box-shadow: 0 4px 16px rgba(34, 69, 32, 0.18), 0 1.5px 6px rgba(0,0,0,0.08);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: box-shadow 0.2s, background 0.2s, transform 0.2s;
  cursor: pointer;
  outline: none;
  opacity: 0;
  visibility: hidden;
  transform: translateY(40px);
}
.upf-fab svg {
  width: 32px;
  height: 32px;
  display: block;
}
#upf-scroll-top {
  background: var(--accent-color);
  color: var(--white-color);
  position: relative;
  overflow: visible;
}
#upf-scroll-top:hover {
  background: var(--primary-color);
  color: var(--white-color);
  box-shadow: 0 6px 24px rgba(241, 94, 37, 0.18), 0 2px 8px rgba(0,0,0,0.10);
  transform: translateY(-4px) scale(1.07);
}

#upf-whatsapp {
	background: #25D366;
	opacity: 1;
	visibility: visible;
	transform: translateY(0);
	transition: opacity 0.3s, transform 0.3s;
	color: var(--white-color);
	}
#upf-whatsapp:hover {
  background: var(--accent-color);
  box-shadow: 0 6px 24px rgba(241, 94, 37, 0.18), 0 2px 8px rgba(0,0,0,0.10);
  transform: translateY(-4px) scale(1.07);
  color: var(--white-color);
}
#upf-whatsapp .fa-whatsapp {
  font-size: 2rem;
  line-height: 1;
  display: block;
}
.upf-fab.upf-visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  transition: opacity 0.3s, transform 0.3s;
}
@media (max-width: 600px) {
  .upf-floating-buttons {
    right: 1rem;
    bottom: 1rem;
    gap: 0.7rem;
  }
  .upf-fab {
    width: 46px;
    height: 46px;
  }
  .upf-fab svg {
    width: 24px;
    height: 24px;
  }
}

/* Flip Card Container */
.flip-card {
  background: transparent;
  width: 100%;
  max-width: 340px;
  margin: 0 auto;
  perspective: 1200px;
  border-radius: 20px;
  box-shadow: 0 6px 24px rgba(0,0,0,0.08);
  transition: box-shadow 0.3s;
  aspect-ratio: 9/16;
  position: relative;
}
.flip-card.flipped {
  box-shadow: 0 12px 32px rgba(0,0,0,0.16);
}
.flip-card.flipped .flip-card-inner {
  transform: rotateY(180deg);
}

/* Flip Card Inner */
.flip-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 0;
  transition: transform 0.6s cubic-bezier(0.4,0.2,0.2,1);
  transform-style: preserve-3d;
  border-radius: 20px;
  overflow: visible;
  aspect-ratio: 9/16;
}

/* Card Faces */
.flip-card-front, .flip-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 20px;
  backface-visibility: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Front Face (Image) */
.flip-card-front {
  z-index: 2;
  background: #fff;
}
.flip-card-front img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
  display: block;
  aspect-ratio: 9/16;
}

/* Back Face (Content) */
.flip-card-back {
  background: #f7f7f7;
  transform: rotateY(180deg);
  color: #222;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 0;
  z-index: 3;
  opacity: 1;
  padding: 32px 10px;
}
.flip-card-back .program-body {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow: visible;
}

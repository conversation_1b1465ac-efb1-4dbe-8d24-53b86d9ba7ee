<?php

error_reporting(E_ALL);
ini_set('display_errors', 1);


require_once __DIR__ . '/../include/initialize.php';

// Include Razorpay PHP SDK
require_once __DIR__ . '/../vendor/razorpay-php/Razorpay.php';

use Razorpay\Api\Api;

// CSRF token validation
if (!isset($_POST['csrf_token']) || !isset($_SESSION['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid CSRF token']);
    exit;
}

// Google reCAPTCHA v2 verification
if (!isset($_POST['g-recaptcha-response']) || empty($_POST['g-recaptcha-response'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'reCAPTCHA verification failed. Please try again.']);
    exit;
}
$recaptcha_response = $_POST['g-recaptcha-response'];
$recaptcha_secret = RECAPTCHA_SECRET_KEY;
$recaptcha_url = 'https://www.google.com/recaptcha/api/siteverify';
$recaptcha = file_get_contents($recaptcha_url . '?secret=' . urlencode($recaptcha_secret) . '&response=' . urlencode($recaptcha_response) . '&remoteip=' . $_SERVER['REMOTE_ADDR']);
$recaptcha = json_decode($recaptcha, true);
if (!$recaptcha['success']) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'reCAPTCHA verification failed.']);
    exit;
}

// Get submitted form data from POST
$donation_amount = filter_input(INPUT_POST, 'donation_amount', FILTER_VALIDATE_INT, ["options" => ["min_range" => 1]]);
$preset_amount = filter_input(INPUT_POST, 'preset_amount', FILTER_VALIDATE_INT, ["options" => ["min_range" => 1]]);
$name = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
$email = filter_input(INPUT_POST, 'email', FILTER_VALIDATE_EMAIL);
$pan = filter_input(INPUT_POST, 'pan', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
$phone = filter_input(INPUT_POST, 'phone', FILTER_SANITIZE_NUMBER_INT);
$address = filter_input(INPUT_POST, 'address', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
$message = filter_input(INPUT_POST, 'message', FILTER_SANITIZE_FULL_SPECIAL_CHARS);

// Determine final amount
$amount = $donation_amount;
if (empty($amount) && !empty($preset_amount)) {
    $amount = $preset_amount;
}

// Validate required fields
if (empty($amount) || empty($name) || empty($email) || empty($phone)) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => 'Missing or invalid required fields.'
    ]);
    exit;
}

// Razorpay API credentials
$razorpay_key_id = 'YOUR_RAZORPAY_KEY_ID'; // TODO: Replace with your Razorpay Key ID
$razorpay_key_secret = 'YOUR_RAZORPAY_KEY_SECRET'; // TODO: Replace with your Razorpay Key Secret

// Create Razorpay Order
try {
    $api = new Api($razorpay_key_id, $razorpay_key_secret);
    $orderData = [
        'receipt'         => 'donation_rcpt_' . time(),
        'amount'          => $amount * 100, // amount in paise
        'currency'        => 'INR',
        'payment_capture' => 1 // auto capture
    ];
    $razorpayOrder = $api->order->create($orderData);
    $order_id = $razorpayOrder['id'];

    // Return order details to frontend
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'order_id' => $order_id,
        'razorpay_key' => $razorpay_key_id,
        'amount' => $amount * 100,
        'currency' => 'INR',
        'name' => $name,
        'email' => $email,
        'phone' => $phone
    ]);
    exit;
} catch (Exception $e) {
    // Log error for debugging
    file_put_contents(__DIR__ . '/../logs/donation-webhook-error.log', date('Y-m-d H:i:s') . ' - Razorpay order creation failed: ' . $e->getMessage() . "\n", FILE_APPEND);
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Razorpay order creation failed. Please try again later.'
    ]);
    exit;
}
// Generic error handler
http_response_code(500);
echo json_encode([
    'success' => false,
    'error' => 'Unknown error occurred.'
]);
exit;

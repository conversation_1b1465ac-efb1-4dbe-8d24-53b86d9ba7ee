<?php

error_reporting(E_ALL);
ini_set('display_errors', 1);


// Include PHPMailer
use P<PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

require_once __DIR__ . '/../vendor/phpmailer/src/Exception.php';
require_once __DIR__ . '/../vendor/phpmailer/src/PHPMailer.php';
require_once __DIR__ . '/../vendor/phpmailer/src/SMTP.php';
require_once __DIR__ . '/../include/initialize.php';

// CSRF token validation
if (!isset($_POST['csrf_token']) || !isset($_SESSION['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Invalid CSRF token']);
    exit;
}

// Google reCAPTCHA v2 verification
if (!isset($_POST['g-recaptcha-response']) || empty($_POST['g-recaptcha-response'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'reCAPTCHA verification failed. Please try again.']);
    exit;
}
$recaptcha_response = $_POST['g-recaptcha-response'];
$recaptcha_secret = RECAPTCHA_SECRET_KEY;
$recaptcha_url = 'https://www.google.com/recaptcha/api/siteverify';
$recaptcha = file_get_contents($recaptcha_url . '?secret=' . urlencode($recaptcha_secret) . '&response=' . urlencode($recaptcha_response) . '&remoteip=' . $_SERVER['REMOTE_ADDR']);
$recaptcha = json_decode($recaptcha, true);
if (!$recaptcha['success']) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'reCAPTCHA verification failed.']);
    exit;
}

// Validate & sanitize POST data
$fullName = filter_input(INPUT_POST, 'full_name', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
$email    = filter_input(INPUT_POST, 'email', FILTER_VALIDATE_EMAIL);
$message  = filter_input(INPUT_POST, 'message', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
$category = filter_input(INPUT_POST, 'category', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
$rating   = filter_input(INPUT_POST, 'rating', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
$recommend = filter_input(INPUT_POST, 'recommend', FILTER_SANITIZE_FULL_SPECIAL_CHARS);

// Basic server-side validation
if (!$fullName || !$email || !$message || !$category || !$rating || !$recommend) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Invalid input']);
    exit;
}

// First submit to Google Form
$postData = [
    "entry.2137584627" => $fullName,
    "entry.1048443022" => $email,
    "entry.213253084" => $category,
    "entry.1842040441" => $rating,
    "entry.1303449869" => $recommend,
    "entry.1538896355"  => $message
];

$ch = curl_init("https://docs.google.com/forms/d/e/1FAIpQLScn1QXasYJ-FtQSkv1oQ1kkK9ZMYJ4dOrTzzm9RIpkzCiZPfA/formResponse");
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true); // Get headers in response
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

// Check Google Form submission result
if ($http_code !== 200) {
    // Optionally, log the error to a file for debugging
    file_put_contents(__DIR__ . '/../logs/feedback-webhook-error.log', date('Y-m-d H:i:s') . " - Google Form submission failed. HTTP code: $http_code. Response: $response\n", FILE_APPEND);
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Failed to save data. Please try again later.']);
    exit;
}

// Send confirmation email to user
$mail = new PHPMailer(true);

try {
    $mail->isSMTP();
    $mail->Host = MAIL_HOST;
    $mail->SMTPAuth = true;
    $mail->Username = MAIL_USERNAME;
    $mail->Password = MAIL_PASSWORD;
    $mail->SMTPSecure = MAIL_ENCRYPTION;
    $mail->Port = MAIL_PORT;

    $mail->setFrom(MAIL_USERNAME, 'UnnyanPath Feedback Team');
    $mail->addAddress($email); // only send to user-entered email
    $mail->addBCC('<EMAIL>'); // BCC to support
    $mail->isHTML(true);
    $mail->Subject = "Thank you for your feedback to UnnyanPath Foundation!";
    $mail->Body = '
        <div style="max-width:600px;margin:0 auto;background:#F8F8F8;border-radius:14px;padding:36px 28px;font-family:Nunito Sans,Inter,sans-serif;color:#224520;box-shadow:0 4px 16px rgba(34,69,32,0.10);border:1.5px solid #e0e0e0;">
            <div style="text-align:center;margin-bottom:28px;">
                <img src="https://unnyanpathfoundation.in/static/images/logo/mail-logo.png" alt="UnnyanPath Foundation Logo" style="height:64px;margin-bottom:14px;">
                <h2 style="margin:0;font-family:Nunito Sans,sans-serif;color:#224520;font-size:2rem;letter-spacing:1px;">UnnyanPath Foundation</h2>
            </div>
            <p style="font-size:16px;line-height:1.8em;color:#224520;margin-bottom:18px;">Dear ' . htmlspecialchars($fullName) . ',<br><br>
            We appreciate you taking the time to share your thoughts with us.<br><br>
            <strong>Your Feedback:</strong><br>
            <b>Name:</b> ' . htmlspecialchars($fullName) . '<br>
            <b>Email:</b> ' . htmlspecialchars($email) . '<br>
            <b>Category:</b> ' . htmlspecialchars($category) . '<br>
            <b>Rating:</b> ' . htmlspecialchars($rating) . '<br>
            <b>Recommend:</b> ' . htmlspecialchars($recommend) . '
            </p>
            <div style="margin-top:32px;text-align:center;">
                <a href="https://unnyanpathfoundation.in/" style="background:#224520;color:#fff;padding:14px 36px;border-radius:8px;text-decoration:none;font-weight:700;font-family:Nunito Sans,sans-serif;display:inline-block;font-size:1rem;transition:background 0.2s;">Visit Our Website</a>
            </div>
            <hr style="margin:36px 0 18px 0;border:0;border-top:1.5px solid #e0e0e0;">
            <div style="text-align:center;font-size:13px;color:#828282;">
                This is an automated email. Please do not reply to this email.<br>
                &copy; <?php echo date("Y"); ?> UnnyanPath Foundation
            </div>
            <div style="text-align:center;">
                <h3 style="font-size:1.1rem;color:#224520;margin-bottom:10px;">Follow us on</h3>
                <a href="https://whatsapp.com/channel/0029VbAyWCWJ93wbTTvz612x" style="margin:0 6px;text-decoration:none;" target="_blank"><img src="https://unnyanpathfoundation.in/static/images/icon/whatsapp.png" alt="WhatsApp" height="28"></a>
                <a href="https://www.linkedin.com/company/unnyanpath-foundation/" style="margin:0 6px;text-decoration:none;" target="_blank"><img src="https://unnyanpathfoundation.in/static/images/icon/linkedin.png" alt="LinkedIn" height="28"></a>
                <a href="https://twitter.com/upf_foundation" style="margin:0 6px;text-decoration:none;" target="_blank"><img src="https://unnyanpathfoundation.in/static/images/icon/twitter.png" alt="Twitter" height="28"></a>
                <a href="https://www.facebook.com/people/Unnyanpath-Foundation/100089804272261/" style="margin:0 6px;text-decoration:none;" target="_blank"><img src="https://unnyanpathfoundation.in/static/images/icon/facebook.png" alt="Facebook" height="28"></a>
                <a href="https://www.instagram.com/unnyanpathfoundation" style="margin:0 6px;text-decoration:none;" target="_blank"><img src="https://unnyanpathfoundation.in/static/images/icon/instagram.png" alt="Instagram" height="28"></a>
                <a href="https://www.youtube.com/@unnyanpathfoundation" style="margin:0 6px;text-decoration:none;" target="_blank"><img src="https://unnyanpathfoundation.in/static/images/icon/youtube.png" alt="YouTube" height="28"></a>
            </div>
        </div>';
    $mail->send();
    header('Content-Type: application/json');
    echo json_encode(['success' => true]);
} catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => $mail->ErrorInfo]);
}
